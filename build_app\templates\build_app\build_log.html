{% extends "admin/base_site.html" %}
{% load static %}

{% block extrastyle %}{{ block.super }}
<style>
    /* Jazzmin兼容样式 */
    .card {
        margin-bottom: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        border: 0;
    }
    
    .card-header {
        background-color: rgba(0, 0, 0, 0.03);
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        padding: 0.75rem 1.25rem;
    }
    
    .card-header h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 500;
    }
    
    .card-body {
        padding: 1.25rem;
    }
    
    /* 表格样式 */
    .table {
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
        border-collapse: collapse;
    }
    
    .table th, .table td {
        padding: 0.75rem;
        vertical-align: top;
        border-top: 1px solid #dee2e6;
    }
    
    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
    }
    
    .table tbody + tbody {
        border-top: 2px solid #dee2e6;
    }
    
    .table-bordered {
        border: 1px solid #dee2e6;
    }
    
    .table-bordered th,
    .table-bordered td {
        border: 1px solid #dee2e6;
    }
    
    /* 标签页导航 */
    .nav {
        display: flex;
        flex-wrap: wrap;
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }
    
    .nav-tabs {
        border-bottom: 1px solid #dee2e6;
    }
    
    .nav-tabs .nav-item {
        margin-bottom: -1px;
    }
    
    .nav-tabs .nav-link {
        border: 1px solid transparent;
        border-top-left-radius: 0.25rem;
        border-top-right-radius: 0.25rem;
        display: block;
        padding: 0.5rem 1rem;
        color: #007bff;
        text-decoration: none;
    }
    
    .nav-tabs .nav-link:hover {
        border-color: #e9ecef #e9ecef #dee2e6;
        text-decoration: none;
    }
    
    .nav-tabs .nav-link.active {
        color: #495057;
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
    }
    
    /* 标签页内容 */
    .tab-content {
        background-color: #fff;
    }
    
    .tab-content > .tab-pane {
        display: none;
    }
    
    .tab-content > .active {
        display: block;
    }
    
    .fade {
        transition: opacity 0.15s linear;
    }
    
    .fade:not(.show) {
        opacity: 0;
    }
    
    /* 日志容器 */
    .log-container {
        background-color: #f8f9fa;
        color: #212529;
        padding: 1rem;
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        overflow: auto;
        height: 500px;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
    }
    
    .log-content {
        white-space: pre-wrap;
        font-size: 0.875rem;
        line-height: 1.5;
    }
    
    /* 调试信息样式 */
    .debug-info {
        background-color: #f8f9fa;
        color: #155724;
        padding: 1rem;
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        white-space: pre-wrap;
        overflow: auto;
        height: 500px;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        line-height: 1.5;
    }
    
    /* 状态和步骤标签 */
    .badge {
        display: inline-block;
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }
    
    .badge-secondary { background-color: #6c757d; color: #fff; }
    .badge-info { background-color: #17a2b8; color: #fff; }
    .badge-success { background-color: #28a745; color: #fff; }
    .badge-danger { background-color: #dc3545; color: #fff; }
    .badge-warning { background-color: #ffc107; color: #212529; }
    .badge-primary { background-color: #007bff; color: #fff; }
    
    /* 自动刷新选项 */
    .auto-refresh {
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
    
    /* 表单控件 */
    .form-check {
        display: block;
        min-height: 1.5rem;
        padding-left: 1.5em;
        margin-bottom: 0.125rem;
    }
    
    .form-check-input {
        width: 1em;
        height: 1em;
        margin-top: 0.25em;
        vertical-align: top;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.25);
        margin-left: -1.5em;
    }
    
    /* 其他实用类 */
    .text-muted {
        color: #6c757d !important;
    }
    
    .auto-refresh label {
        display: flex;
        align-items: center;
        gap: 6px;
        cursor: pointer;
        font-weight: normal;
        font-size: 13px;
    }
    
    /* 自定义复选框 */
    .auto-refresh input[type="checkbox"] {
        margin-right: 3px;
        width: 14px;
        height: 14px;
    }
    
    /* 最后更新时间 */
    #lastUpdated {
        color: #666;
        font-size: 13px;
    }
    
    /* 滚动条样式 - 使用默认滚动条 */
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        .build-info table th {
            width: 25%;
        }
        
        .log-container, .debug-info {
            height: 350px;
        }
        
        .nav-tabs .nav-link {
            padding: 6px 10px;
            font-size: 12px;
        }
    }
    
    /* 面包屑导航 */
    .breadcrumbs {
        padding: 8px 15px;
        margin-bottom: 10px;
        background-color: #f8f8f8;
        border-radius: 0;
        font-size: 13px;
        border-bottom: 1px solid #ddd;
    }
    
    .breadcrumbs a {
        color: #0d6efd;
        text-decoration: none;
    }
    
    .breadcrumbs a:hover {
        text-decoration: underline;
    }
</style>
{% endblock %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">首页</a></li>
    <li class="breadcrumb-item"><a href="{% url 'admin:app_list' 'build_app' %}">构建应用</a></li>
    <li class="breadcrumb-item"><a href="{% url 'admin:build_app_buildjob_changelist' %}">构建任务列表</a></li>
    <li class="breadcrumb-item active">任务 #{{ build_job.id }} 日志</li>
</ol>
{% endblock %}

{% block content %}
<!-- 存储构建任务数据供JavaScript使用 -->
<div id="build-job-data" data-job-id="{{ build_job.id }}" style="display:none;"></div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 构建任务信息卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3>构建任务 #{{ build_job.id }} 详情</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 15%">项目</th>
                                    <td>{{ build_job.project.name }}</td>
                                    <th style="width: 15%">配置</th>
                                    <td>{{ build_job.app_config.config_name }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if build_job.status == 'PENDING' %}
                                            <span id="status-badge" class="badge badge-secondary">{{ build_job.get_status_display }}</span>
                                        {% elif build_job.status == 'BUILDING' %}
                                            <span id="status-badge" class="badge badge-info">{{ build_job.get_status_display }}</span>
                                        {% elif build_job.status == 'SUCCESS' %}
                                            <span id="status-badge" class="badge badge-success">{{ build_job.get_status_display }}</span>
                                        {% elif build_job.status == 'FAILED' %}
                                            <span id="status-badge" class="badge badge-danger">{{ build_job.get_status_display }}</span>
                                        {% else %}
                                            <span id="status-badge" class="badge badge-secondary">{{ build_job.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <th>当前步骤</th>
                                    <td>
                                        {% if build_job.current_step == 'CONFIG' %}
                                            <span id="step-badge" class="badge badge-primary">{{ build_job.get_current_step_display }}</span>
                                        {% elif build_job.current_step == 'COCOS' %}
                                            <span id="step-badge" class="badge badge-info">{{ build_job.get_current_step_display }}</span>
                                        {% elif build_job.current_step == 'ANDROID' %}
                                            <span id="step-badge" class="badge badge-warning">{{ build_job.get_current_step_display }}</span>
                                        {% elif build_job.current_step == 'COMPLETED' %}
                                            <span id="step-badge" class="badge badge-success">{{ build_job.get_current_step_display }}</span>
                                        {% else %}
                                            <span id="step-badge" class="badge badge-secondary">{{ build_job.get_current_step_display }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>分支/标签</th>
                                    <td>{{ build_job.branch_or_tag }}</td>
                                    <th>触发人</th>
                                    <td>{{ build_job.triggered_by.username }}</td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{ build_job.created|date:"Y-m-d H:i:s" }}</td>
                                    <th>输出路径</th>
                                    <td>{{ build_job.output_file_path|default:"暂无" }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="auto-refresh">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                            <label class="form-check-label" for="autoRefresh">
                                自动刷新
                            </label>
                        </div>
                        <span id="lastUpdated" class="text-muted">最后更新: {{ build_job.created|date:"Y-m-d H:i:s" }}</span>
                    </div>
                </div>
            </div>
            
            <!-- 日志标签页 -->
            <div class="card">
                <div class="card-body p-0">
                    <ul class="nav nav-tabs" id="logTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="standard-tab" data-toggle="tab" href="#standard" role="tab">标准日志</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="debug-tab" data-toggle="tab" href="#debug" role="tab">调试信息</a>
                        </li>
                    </ul>
                    
                    <div class="tab-content" id="logTabsContent">
                        <div class="tab-pane fade show active" id="standard" role="tabpanel">
                            <div class="log-container">
                                <pre id="logContent" class="log-content">{{ build_job.log }}</pre>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="debug" role="tabpanel">
                            <div class="debug-info">
                                <pre id="debugContent" class="log-content">{{ build_job.debug_info|default:"暂无调试信息" }}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascript %}
<script>
    // 切换标签页
    document.addEventListener('DOMContentLoaded', function() {
        var tabLinks = document.querySelectorAll('.nav-link');
        var tabPanes = document.querySelectorAll('.tab-pane');
        
        tabLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有标签页的激活状态
                tabLinks.forEach(function(tab) {
                    tab.classList.remove('active');
                });
                
                tabPanes.forEach(function(pane) {
                    pane.classList.remove('show');
                    pane.classList.remove('active');
                });
                
                // 激活当前标签页
                this.classList.add('active');
                var tabId = this.getAttribute('href').substring(1);
                var activePane = document.getElementById(tabId);
                activePane.classList.add('show');
                activePane.classList.add('active');
            });
        });
        
        // 从页面元素中获取构建任务ID
        var buildJobId = parseInt(document.getElementById('build-job-data').getAttribute('data-job-id'));
        var autoRefreshCheckbox = document.getElementById('autoRefresh');
        var lastUpdatedSpan = document.getElementById('lastUpdated');
        var logContent = document.getElementById('logContent');
        var debugContent = document.getElementById('debugContent');
        var statusBadge = document.getElementById('status-badge');
        var stepBadge = document.getElementById('step-badge');
        var refreshInterval;
        
        function updateLogContent() {
            fetch('/build_app/ajax/build_log/' + buildJobId + '/')
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    // 更新日志内容
                    logContent.textContent = data.log;
                    debugContent.textContent = data.debug_info || '暂无调试信息';
                    
                    // 更新状态和步骤
                    statusBadge.className = 'status-badge status-' + data.status;
                    statusBadge.textContent = getStatusText(data.status);
                    
                    stepBadge.className = 'step-badge step-' + data.current_step;
                    stepBadge.textContent = getStepText(data.current_step);
                    
                    // 更新最后更新时间
                    lastUpdatedSpan.textContent = '最后更新: ' + data.updated;
                    
                    // 自动滚动到日志底部
                    var logContainer = logContent.parentElement;
                    logContainer.scrollTop = logContainer.scrollHeight;
                    
                    // 如果构建已完成，停止自动刷新
                    if (data.is_completed && autoRefreshCheckbox.checked) {
                        autoRefreshCheckbox.checked = false;
                        clearInterval(refreshInterval);
                    }
                })
                .catch(function(error) {
                    console.error('更新日志时出错:', error);
                });
        }
        
        function getStatusText(status) {
            var statusMap = {
                'PENDING': '等待中',
                'BUILDING': '构建中',
                'SUCCESS': '成功',
                'FAILED': '失败'
            };
            return statusMap[status] || status;
        }
        
        function getStepText(step) {
            var stepMap = {
                'CONFIG': '配置',
                'COCOS': 'Cocos构建',
                'ANDROID': 'Android构建',
                'COMPLETED': '已完成'
            };
            return stepMap[step] || step;
        }
        
        function toggleAutoRefresh() {
            if (autoRefreshCheckbox.checked) {
                refreshInterval = setInterval(updateLogContent, 3000); // 每3秒刷新一次
                updateLogContent(); // 立即更新一次
            } else {
                clearInterval(refreshInterval);
            }
        }
        
        autoRefreshCheckbox.addEventListener('change', toggleAutoRefresh);
        
        // 初始化自动刷新
        toggleAutoRefresh();
    });
</script>
{% endblock %}
