"""
自动上传模块 - 构建完成后自动上传APK到远程服务器
"""
import os
import logging
import paramiko
from pathlib import Path
from django.utils import timezone

from ..models import AppConfig, BuildJob, ServerInfo, UploadedFile, Project
from ..admin_views import _get_download_link

logger = logging.getLogger(__name__)

def check_auto_upload_option(build_job):
    """
    检查构建任务是否设置了自动上传选项
    
    Args:
        build_job: BuildJob对象
        
    Returns:
        bool: 是否需要自动上传
    """
    if not build_job:
        return False
        
    # 直接使用auto_upload字段
    return build_job.auto_upload

def get_server_info_for_company(company):
    """
    获取公司的服务器信息
    
    Args:
        company: Company对象
        
    Returns:
        tuple: (server_info, error_message)
               server_info是ServerInfo对象
               error_message是错误信息，如果成功则为None
    """
    if not company:
        return None, "未指定公司信息"
    
    try:
        # 优先获取默认服务器
        server_info = ServerInfo.objects.filter(company=company, is_default=True).first()
        
        # 如果没有默认服务器，获取最近使用的服务器
        if not server_info:
            server_info = ServerInfo.objects.filter(company=company).order_by('-last_used').first()
            
        if not server_info:
            return None, f"公司 {company.name} 没有配置任何服务器信息"
            
        return server_info, None
        
    except Exception as e:
        logger.error(f"获取公司服务器信息时出错: {str(e)}")
        return None, f"获取服务器信息时出错: {str(e)}"

def auto_upload_apk(build_job_id):
    """
    自动上传APK文件到远程服务器
    
    Args:
        build_job_id: 构建任务ID
        
    Returns:
        tuple: (success, message)
    """
    # 获取构建任务信息
    try:
        build_job = BuildJob.objects.get(pk=build_job_id)
    except BuildJob.DoesNotExist:
        logger.error(f"未找到构建任务 ID: {build_job_id}")
        return False, f"未找到构建任务 ID: {build_job_id}"
        
    # 检查构建任务状态
    if build_job.status != BuildJob.Status.SUCCESS:
        return False, f"构建任务未成功完成，当前状态: {build_job.status}"
        
    # 检查是否需要自动上传
    if not check_auto_upload_option(build_job):
        return False, "没有启用自动上传选项"
        
    # 检查是否有输出文件路径
    if not build_job.output_file_path:
        return False, "构建任务没有输出文件路径"
    
    # 获取应用配置和公司信息
    app_config = build_job.app_config
    if not app_config:
        return False, "构建任务没有关联的应用配置"
        
    company = app_config.company
    if not company:
        return False, "应用配置没有关联的公司信息"
    
    # 获取服务器信息
    server_info, error = get_server_info_for_company(company)
    if error:
        return False, error
    
    # 记录自动上传开始信息
    logger.info(f"开始自动上传APK - 构建任务ID: {build_job_id}, 服务器: {server_info.host}")
    build_job.log += f"\n\n=== 自动上传APK开始 ===\n"
    build_job.log += f"目标服务器: {server_info.host}:{server_info.port}\n"
    build_job.log += f"上传目录: {server_info.remote_path}\n"
    build_job.save(update_fields=['log'])
    
    # 获取要上传的APK文件路径
    output_dir = Path(build_job.output_file_path)
    apk_files = []
    
    # 查找并排序 APK 文件，只获取最新的一个
    try:
        # 收集所有 APK 文件
        for file in output_dir.glob("*.apk"):
            apk_files.append(file)
            
        # 按文件修改时间排序，最新的在前
        apk_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 只保留最新的一个 APK
        if apk_files:
            apk_files = [apk_files[0]]
            build_job.log += f"已选择最新的 APK 文件进行上传: {apk_files[0].name}\n"
            build_job.save(update_fields=['log'])
    except Exception as e:
        error_msg = f"查找和排序 APK 文件时出错: {str(e)}"
        build_job.log += f"{error_msg}\n"
        build_job.save(update_fields=['log'])
        return False, error_msg
    
    if not apk_files:
        error_msg = f"在输出目录 {output_dir} 中未找到 APK 文件"
        build_job.log += f"{error_msg}\n"
        build_job.save(update_fields=['log'])
        return False, error_msg
    
    # 只上传最新的 APK 文件
    uploaded_files = []
    for apk_file in apk_files:
        try:
            # 使用Paramiko通过SFTP上传文件
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # 连接到远程服务器
            ssh_client.connect(
                hostname=server_info.host,
                port=server_info.port,
                username=server_info.username,
                password=server_info.password,
                timeout=10
            )

            # 创建SFTP客户端
            sftp_client = ssh_client.open_sftp()

            # 确保远程目录存在
            try:
                sftp_client.stat(server_info.remote_path)
            except FileNotFoundError:
                # 创建远程目录
                command = f"mkdir -p {server_info.remote_path}"
                stdin, stdout, stderr = ssh_client.exec_command(command)
                exit_status = stdout.channel.recv_exit_status()
                if exit_status != 0:
                    error_msg = stderr.read().decode('utf-8')
                    build_job.log += f"创建远程目录失败: {error_msg}\n"
                    build_job.save(update_fields=['log'])
                    sftp_client.close()
                    ssh_client.close()
                    continue

            # 上传文件
            remote_file_path = f"{server_info.remote_path}/{apk_file.name}"
            sftp_client.put(str(apk_file), remote_file_path)
            
            # 添加到已上传列表
            uploaded_files.append(apk_file.name)
            
            # 记录日志
            build_job.log += f"已上传文件: {apk_file.name} -> {server_info.host}:{remote_file_path}\n"
            build_job.save(update_fields=['log'])

            # 关闭连接
            sftp_client.close()
            ssh_client.close()
            
            # 更新服务器最后使用时间
            server_info.last_used = timezone.now()
            server_info.save(update_fields=['last_used'])
            
            # 保存上传记录
            try:
                
                # 获取项目信息
                project_name = "未知项目"
                try:
                    # 直接从 BuildJob 获取项目信息
                    if build_job.project:
                        project_name = build_job.project.name
                    else:
                        project_name = app_config.config_name
                except Exception as e:
                    logger.warning(f"获取项目信息时出错: {e}")
                    project_name = app_config.config_name
                
                # 构建下载链接
                company = app_config.company
                download_link = _get_download_link(company.download_link, app_config.base_url)
                
                # 如果有效，添加文件名
                if download_link:
                    download_link = f"{download_link}/{apk_file.name}"
                
                # 计算文件大小
                file_size = apk_file.stat().st_size
                
                # 创建上传记录
                UploadedFile.objects.create(
                    company_name=company.name,
                    project_name=project_name,
                    config_name=app_config.config_name,
                    app_name=app_config.app_name,
                    file_name=apk_file.name,
                    file_size=file_size,
                    download_link=download_link or "",
                    remote_path=remote_file_path,
                    remote_server=server_info.host
                )
                
                logger.info(f"已保存自动上传记录: {app_config.app_name} - {apk_file.name}")
                build_job.log += f"已添加上传记录到数据库\n"
                build_job.save(update_fields=['log'])
            except Exception as e:
                logger.error(f"保存上传记录失败: {e}")
                build_job.log += f"保存上传记录失败: {str(e)}\n"
                build_job.save(update_fields=['log'])
        except Exception as e:
            error_msg = f"上传文件 {apk_file.name} 时出错: {str(e)}"
            logger.error(error_msg)
            build_job.log += f"{error_msg}\n"
            build_job.save(update_fields=['log'])
    
    # 完成自动上传
    if uploaded_files:
        build_job.log += f"\n=== 自动上传完成 ===\n"
        build_job.log += f"成功上传 {len(uploaded_files)} 个文件: {', '.join(uploaded_files)}\n"
        build_job.save(update_fields=['log'])
        return True, f"成功上传 {len(uploaded_files)} 个文件"
    else:
        build_job.log += f"\n=== 自动上传失败 ===\n"
        build_job.log += f"没有成功上传任何文件\n"
        build_job.save(update_fields=['log'])
        return False, "没有成功上传任何文件"
