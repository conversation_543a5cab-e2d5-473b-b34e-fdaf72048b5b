"""
Android 构建模块，负责 Android Gradle 项目的构建流程。
"""

import os
import logging
from pathlib import Path
import shutil

from celery import shared_task
from django.conf import settings

from ..models import BuildJob, AppConfig
from .base import (
    run_command,
    get_system_config,
    ensure_directory,
    check_job_canceled,
    run_command_with_cancel_support,
)
from .lock import release_project_lock

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=2)
def build_android_project(self, build_job_id):
    """构建 Android 项目。

    此任务负责 Android Gradle 项目的构建流程，执行 Gradle 构建命令
    并处理构建产物。构建过程中会更新构建任务的状态和日志。
    支持在构建过程中任意时刻取消任务。

    Args:
        build_job_id: 构建任务 ID

    Returns:
        执行结果消息
    """
    # 先检查任务是否已被取消
    if check_job_canceled(build_job_id):
        logger.info(f"任务 {build_job_id} 已被取消，跳过 Android 构建执行")
        try:
            build_job = BuildJob.objects.get(pk=build_job_id)
            build_job.log += "\n\n[系统通知] Android 构建阶段被取消\n"
            build_job.status = BuildJob.Status.CANCELED
            build_job.save(update_fields=["status", "log"])

            # 在项目有锁的情况下释放锁
            if build_job.project:
                release_project_lock(build_job.project.id)
        except Exception as e:
            logger.error(f"更新已取消任务状态时出错: {e}")
        return "任务已被取消"

    build_job = None
    try:
        # --- 获取构建任务 ---
        build_job = BuildJob.objects.get(pk=build_job_id)
        project = build_job.project
        app_config = build_job.app_config

        # --- 更新构建任务状态为 BUILDING ---
        if build_job.status != BuildJob.Status.BUILDING:
            build_job.status = BuildJob.Status.BUILDING
            build_job.save(update_fields=["status"])

        build_job.current_step = BuildJob.BuildStep.ANDROID
        build_job.log += f"\n\n========== ANDROID 构建阶段 ==========\n"
        build_job.log += f"时间: {build_job.created.strftime('%Y-%m-%d %H:%M:%S')}\n"
        build_job.save(update_fields=["current_step", "log"])

        # --- ANDROID 配置注入 ---
        build_job.log += f"\n正在为 Android 构建注入配置...\n"
        build_job.save(update_fields=["log"])

        try:
            # 生成 Android 配置文件 (release.properties)
            props_file_path = os.path.join(project.local_path, "release.properties")

            from ..utils import generate_android_properties

            # 使用定制函数生成 Android 配置
            props_content_generated = generate_android_properties(app_config, build_job)

            # 将生成的配置内容写入文件
            with open(props_file_path, "w", encoding="utf-8") as f:
                f.write(props_content_generated)

            # 记录配置文件位置到日志
            build_job.log += f"\n成功生成 Android 配置文件: {props_file_path}\n"

            # 读取生成的或存在的配置文件内容并写入调试日志
            try:
                with open(props_file_path, "r", encoding="utf-8") as f:
                    props_content = f.read()

                # 将配置内容写入调试日志
                debug_info = build_job.debug_info or ""
                debug_info += f"\n--- Android 配置内容 ---\n"
                debug_info += f"{props_content}\n"
                debug_info += f"--- 配置内容结束 ---\n\n"
                build_job.debug_info = debug_info
            except Exception as read_e:
                build_job.log += f"\n警告: 无法读取配置文件内容: {read_e}\n"

            build_job.save(update_fields=["log", "debug_info"])

        except Exception as e:
            error_msg = f"注入 Android 配置失败: {e}"
            logger.error(error_msg)
            build_job.log += f"\n{error_msg}\n"
            build_job.status = BuildJob.Status.FAILED
            build_job.save(update_fields=["status", "log"])
            return error_msg

        # --- 继续检查当前任务状态 ---
        if build_job.status == BuildJob.Status.FAILED:
            # 如果任务已经失败，不再继续
            error_msg = f"任务状态为 FAILED，不继续执行"
            logger.error(error_msg)
            build_job.log += f"\n{error_msg}\n"
            build_job.save(update_fields=["log"])
            return error_msg

        # --- 检查当前任务步骤，确保是 ANDROID 阶段 ---
        if build_job.current_step != BuildJob.BuildStep.ANDROID:
            # 非法的步骤跳转，可能是由于并发任务造成的
            error_msg = (
                f"错误: 当前任务步骤({build_job.current_step})不是ANDROID阶段，拒绝执行"
            )
            logger.error(error_msg)
            build_job.log += f"\n{error_msg}\n"
            build_job.status = BuildJob.Status.FAILED
            build_job.save(update_fields=["status", "log"])
            return error_msg

        # 允许 Cocos 和 Android 类型都能进行 Android 构建
        app_type = _validate_app_config(build_job, app_config)

        logger.info(f"开始 Android 项目构建，任务 ID: {build_job_id}")

        # --- 构建调试日志 ---
        _set_debug_info(build_job, project, app_type)

        # --- 更新任务状态 ---
        build_job.status = BuildJob.Status.BUILDING
        build_job.log += f"开始 Android 项目构建 (Task #{build_job_id} Step: {build_job.current_step})...\n"
        build_job.save(update_fields=["status", "log", "debug_info"])

        # --- 确定项目路径 ---
        project_path = project.local_path
        if not project_path or not os.path.isdir(project_path):
            raise ValueError(f"项目路径无效: {project_path}")

        # --- 找到 Gradle 项目根目录 ---
        # 先尝试从 Cocos 构建日志中查找 Android 项目路径
        gradle_root, output_name = _find_android_project_path(build_job, project_path)

        # --- 使用固定构建参数 release 模式 ---
        build_job.log += f"使用固定参数：release 模式\n"

        # --- 准备 Gradle 命令 ---
        cmd_str = _prepare_gradle_command(gradle_root)

        build_job.log += f"执行 Gradle 构建命令: {cmd_str}\n"
        build_job.log += f"当前工作目录: {gradle_root}\n"
        build_job.save(update_fields=["log"])

        # 检查任务是否已被取消
        if check_job_canceled(build_job.id):
            build_job.log += "\n\n[系统通知] Android Gradle构建被取消\n"
            build_job.status = BuildJob.Status.CANCELED
            build_job.save(update_fields=["status", "log"])
            return "任务已被取消"

        # 使用支持取消的命令执行方式
        build_job.log += "开始执行Gradle命令（支持随时取消）...\n"
        build_job.save(update_fields=["log"])
        success, output, return_code = run_command_with_cancel_support(
            cmd_str, build_job.id, cwd=gradle_root, use_shell=True, check_interval=3
        )

        if not success:
            build_job.status = BuildJob.Status.FAILED
            build_job.log += f"Gradle 构建失败:\n{output}\n"
            build_job.save(update_fields=["status", "log"])
            return f"Android 项目构建失败: {output}"

        # --- 处理构建结果 ---
        build_job.log += f"Gradle 构建成功！\n"

        # 从系统配置中获取 cocos_build_name
        cocos_build_name = get_system_config("cocos_build_name", "game")

        # 根据约定构建完整路径：build\{output_name}\proj\build\{cocos_build_name}\outputs\apk\release
        project_base_path = os.path.dirname(gradle_root)  # 获取项目根目录
        expected_apk_path, alt_paths = _determine_apk_paths(
            project_base_path, gradle_root, output_name, cocos_build_name
        )

        build_job.log += f"APK 文件应该位于约定路径: {expected_apk_path}\n"

        # 从系统配置获取文件存储根路径
        system_file_path = get_system_config("system_file_path", "")

        isOk = _copy_apk_files(
            build_job, expected_apk_path, alt_paths, system_file_path
        )

        if isOk:
            # --- 更新调试信息 ---
            debug_info = build_job.debug_info or ""
            debug_info += f"\n[{build_job.created.strftime('%Y-%m-%d %H:%M:%S')}] ANDROID -> COMPLETED \n"
            debug_info += f"APK输出路径: {build_job.output_file_path}\n"
            debug_info += f"构建任务完成\n"
            build_job.debug_info = debug_info

            # --- 更新任务状态，将当前步骤设置为 COMPLETED ---
            build_job.current_step = BuildJob.BuildStep.COMPLETED
            build_job.status = BuildJob.Status.SUCCESS
            build_job.log += f"\n构建任务完成，所有步骤已成功执行\n"
            build_job.save(
                update_fields=[
                    "status",
                    "log",
                    "output_file_path",
                    "debug_info",
                    "current_step",
                ]
            )
            
            # 尝试自动上传APK文件
            try:
                from .auto_upload import auto_upload_apk, check_auto_upload_option
                
                # 检查是否需要自动上传
                if check_auto_upload_option(build_job):
                    logger.info(f"开始自动上传APK - 构建任务ID: {build_job.id}")
                    success, message = auto_upload_apk(build_job.id)
                    logger.info(f"自动上传结果 - 成功: {success}, 消息: {message}")
            except Exception as e:
                logger.error(f"尝试自动上传时发生错误: {str(e)}")
                # 自动上传失败不应影响构建任务的成功状态

            # 释放项目锁，允许下一个任务开始
            if build_job.project:
                release_project_lock(build_job.project.id)
                logger.info(f"构建成功，已释放项目锁 {build_job.project.id}")
        else:
            build_job.status = BuildJob.Status.FAILED
            build_job.log += f"\n未能复制 APK 文件到目标目录\n"
            build_job.save(update_fields=["status", "log"])

            # 即使失败也释放项目锁
            if build_job.project:
                release_project_lock(build_job.project.id)
                logger.info(f"构建失败，已释放项目锁 {build_job.project.id}")

        return "Android 项目构建完成"

    except BuildJob.DoesNotExist:
        logger.error(f"未找到 ID 为 {build_job_id} 的构建任务")
        return f"未找到 ID 为 {build_job_id} 的构建任务"

    except ValueError as ve:
        logger.error(f"Android 构建任务 {build_job_id} 参数错误: {ve}")
        if build_job:
            build_job.status = BuildJob.Status.FAILED
            build_job.log += f"\n错误: {ve}\n"
            build_job.save(update_fields=["status", "log"])
        return f"Android 构建参数错误: {ve}"

    except Exception as e:
        logger.exception(f"Android 构建任务 {build_job_id} 发生意外错误")
        if build_job:
            build_job.status = BuildJob.Status.FAILED
            build_job.log += f"\n构建过程中发生错误: {e}\n"
            build_job.save(update_fields=["status", "log"])

            # 异常情况下释放项目锁
            if build_job.project:
                release_project_lock(build_job.project.id)
                logger.info(f"由于异常已释放项目锁 {build_job.project.id}")

        # 尝试重试
        try:
            self.retry(exc=e, countdown=60)
        except self.MaxRetriesExceededError:
            logger.error(f"构建任务 {build_job_id} 超出最大重试次数")
            return f"Android 构建失败，超出重试次数: {e}"

        return f"Android 构建过程中出错，正在重试: {e}"


# ========= 辅助函数定义 =========


def _find_gradle_root(project_path):
    """查找 Gradle 项目根目录。

    Args:
        project_path: 项目路径

    Returns:
        Gradle 项目根目录，如果未找到则返回 None
    """
    project_dir = Path(project_path)

    # 首先检查项目根目录
    if any(project_dir.glob("build.gradle*")):
        return str(project_dir)

    # 查找可能的 Android 项目目录
    for pattern in ["**/build.gradle", "**/build.gradle.kts"]:
        for gradle_file in project_dir.glob(pattern):
            # 返回包含 gradle 文件的目录
            return str(gradle_file.parent)

    # 检查是否有 Android 目录结构
    android_dirs = list(project_dir.glob("**/app/src/main/AndroidManifest.xml"))
    if android_dirs:
        # 返回 app 目录的父目录
        return str(android_dirs[0].parent.parent.parent.parent)

    return None


def _validate_app_config(build_job, app_config):
    """验证应用配置是否有效。

    Args:
        build_job: 构建任务对象
        app_config: 应用配置对象

    Returns:
        应用类型

    Raises:
        ValueError: 当应用配置无效时抛出
    """
    from ..models import AppConfig

    app_type = app_config.get_app_type() if app_config else None
    build_job_id = build_job.id

    if (not app_config) or (
        app_type != AppConfig.ProjectType.ANDROID
        and app_type != AppConfig.ProjectType.COCOS
    ):
        logger.warning(f"构建任务 {build_job_id} 类型为 {app_type}")
        raise ValueError(
            f"构建任务 {build_job_id} 不是 Android 或 Cocos 项目，或未指定应用配置"
        )

    return app_type


def _find_android_project_path(build_job, project_path):
    """从日志中查找 Android 项目路径或使用默认规则定位。

    Args:
        build_job: 构建任务对象
        project_path: 项目路径

    Returns:
        (gradle_root, output_name): Gradle 项目根目录和输出名称
    """
    # 从日志提取路径信息
    android_project_path = None
    output_name = None

    if build_job.log:
        for line in build_job.log.splitlines():
            if line.startswith("ANDROID_PROJECT_PATH="):
                android_project_path = line.split("=", 1)[1].strip()
                path_parts = android_project_path.split(os.sep)
                if len(path_parts) >= 3 and path_parts[-3] == "build":
                    output_name = path_parts[-2]
                build_job.log += (
                    f"使用通过日志传递的 Android 项目路径: {android_project_path}\n"
                )
                break

            # 兼容旧格式
            elif (
                "找到 Android 项目路径:" in line
                or "使用约定的 Android 项目路径:" in line
            ):
                if "找到 Android 项目路径:" in line:
                    android_project_path = line.split("找到 Android 项目路径:", 1)[
                        1
                    ].strip()
                else:
                    android_project_path = line.split(
                        "使用约定的 Android 项目路径:", 1
                    )[1].strip()
                build_job.log += f"使用 Cocos 构建任务提供的 Android 项目路径: {android_project_path}\n"
                break

    # 如果无法从日志获取，尝试使用 app_config 获取 output_name
    if not output_name and build_job.app_config:
        output_name = build_job.app_config.cocos_ad_platform

    # 需要查找 Gradle 项目根目录
    if not android_project_path or not os.path.isdir(android_project_path):
        # 如果没有直接指定路径，则使用默认规则查找
        gradle_root = _find_gradle_root(project_path)
        if not gradle_root:
            raise ValueError(f"在项目路径中找不到有效的 Gradle 项目: {project_path}")
        build_job.log += f"找到 Gradle 项目根目录: {gradle_root}\n"
    else:
        gradle_root = android_project_path

    # 如果有约定的 proj 子目录，使用它
    proj_path = os.path.join(gradle_root, "proj")
    if os.path.isdir(proj_path):
        gradle_root = proj_path
        build_job.log += f"使用约定的 proj 子目录: {gradle_root}\n"
    else:
        build_job.log += f"警告: 未找到约定的 proj 子目录: {proj_path}\n"
        # 尝试创建目录结构
        try:
            os.makedirs(proj_path, exist_ok=True)
            build_job.log += f"已创建 proj 子目录: {proj_path}\n"
            gradle_root = proj_path
        except Exception as e:
            build_job.log += f"创建 proj 目录失败: {e}\n"

    return gradle_root, output_name


def _prepare_gradle_command(gradle_root):
    """准备 Gradle 命令

    Args:
        gradle_root: Gradle 项目根目录

    Returns:
        gradle_cmd: Gradle 命令字符串
    """
    # 在 Windows 环境下尝试多种可能的 Gradle 命令
    possible_gradle_paths = [
        os.path.join(gradle_root, "gradlew.bat"),
        os.path.join(gradle_root, "gradle", "wrapper", "gradlew.bat"),
        # 可能安装在系统路径中的 Gradle
        "C:\\Gradle\\gradle-7.3.3\\bin\\gradle.bat",
        "C:\\Gradle\\gradle-7.6\\bin\\gradle.bat",
        "C:\\Program Files\\Gradle\\bin\\gradle.bat",
    ]

    gradle_cmd = None
    for path in possible_gradle_paths:
        if os.path.isfile(path):
            gradle_cmd = path
            break

    # 如果找不到具体的 Gradle 路径，则使用项目自带的 Gradle
    if not gradle_cmd:
        # Windows 环境使用 gradlew.bat
        gradle_cmd = "gradlew.bat" if os.name == "nt" else "./gradlew"

    # Windows 环境下需要特别处理
    if os.name == "nt":
        # 在 Windows 下，需要在当前目录下使用 ./gradlew.bat 格式
        cmd_str = ".\\gradlew.bat assembleRelease --stacktrace"
    else:
        # Linux/Mac 环境
        cmd_str = "./gradlew assembleRelease --stacktrace"

    return cmd_str


def _determine_apk_paths(project_base_path, gradle_root, output_name, cocos_build_name):
    """确定 APK 路径和替代搜索路径

    Args:
        project_base_path: 项目基础路径
        gradle_root: Gradle 项目根目录
        output_name: 输出名称
        cocos_build_name: Cocos 构建名称

    Returns:
        (expected_apk_path, alt_paths): 预期 APK 路径和替代搜索路径列表
    """
    # 根据约定构建完整路径：build\{output_name}\proj\build\{cocos_build_name}\outputs\apk\release
    expected_apk_path = os.path.join(
        project_base_path,
        "build",
        output_name,
        "proj",
        "build",
        cocos_build_name,
        "outputs",
        "apk",
        "release",
    )

    # 尝试查找其他可能的路径
    alt_paths = [
        # 约定路径的变体
        os.path.join(
            project_base_path,
            "build",
            output_name,
            "proj",
            "build",
            cocos_build_name,
            "outputs",
            "apk",
        ),  # 不含 release
        os.path.join(
            project_base_path,
            "build",
            output_name,
            "proj",
            "build",
            "outputs",
            "apk",
            "release",
        ),  # 不含 cocos_build_name
        os.path.join(
            project_base_path,
            "build",
            output_name,
            "proj",
            "app",
            "build",
            "outputs",
            "apk",
            "release",
        ),  # 另一种常见结构
        # 旧约定路径
        os.path.join(gradle_root, "app", "build", "outputs", "apk"),
        os.path.join(gradle_root, "build", "outputs", "apk"),
        # 更宽松的搜索路径
        os.path.join(project_base_path, "build"),
        gradle_root,
    ]

    return expected_apk_path, alt_paths


def _copy_apk_file_from_path(build_job, source_path, target_dir):
    """从指定路径查找并复制APK文件到目标目录

    Args:
        build_job: 构建任务对象
        source_path: 源路径
        target_dir: 目标目录

    Returns:
        list: 成功复制的文件列表
    """
    copied_files = []

    if not os.path.exists(source_path):
        return copied_files

    build_job.log += f"正在从路径查找APK: {source_path}\n"
    for root, _, files in os.walk(source_path):
        for file in files:
            if file.endswith(".apk"):
                source_file = os.path.join(root, file)

                # 处理文件名，将前缀替换为应用名称的拼音
                app_config = build_job.app_config
                app_name = app_config.app_name

                # 原名是这样的: mengdian-5.3.0-20250521-10.27.28-release.apk
                # 萌电米多是app_name, 现在我需要把原名修改成“萌电米多”的拼音替换成
                # mengdian-5.3.0-20250521-10.27.28-release.apk
                # -> mengdianmiduo-5.3.0-20250521-10.27.28-release.apk

                # 获取应用名称的拼音
                app_name_pinyin = _get_app_name_pinyin(app_name)

                # 分析原文件名，替换前缀部分
                new_file_name = file

                # 只有在成功获取拼音且文件名包含'-'时才进行替换
                if app_name_pinyin and "-" in file:
                    prefix, suffix = file.split("-", 1)  # 只分割第一个'-'
                    new_file_name = f"{app_name_pinyin}-{suffix}"
                    build_job.log += f"重命名APK文件: {file} -> {new_file_name}\n"
                else:
                    # 如果无法获取拼音或文件名格式不符合预期，保留原名
                    build_job.log += f"保留原APK文件名: {file}\n"

                # 如果有代理码，添加到文件开头
                invite_code = app_config.invite_code or ""
                if invite_code:
                    new_file_name = f"{invite_code}-{new_file_name}"

                target_file = os.path.join(target_dir, new_file_name)
                try:
                    shutil.copy2(source_file, target_file)
                    copied_files.append(target_file)
                    build_job.log += f"已复制APK文件到: {target_file}\n"

                    # 复制成功后删除源文件
                    try:
                        os.remove(source_file)
                        build_job.log += f"已删除源APK文件: {source_file}\n"
                    except Exception as e:
                        build_job.log += (
                            f"删除源APK文件失败: {source_file}, 原因: {e}\n"
                        )
                except Exception as e:
                    build_job.log += (
                        f"复制APK文件失败: {source_file} -> {target_file}, 原因: {e}\n"
                    )

    return copied_files


def _copy_apk_files(build_job, expected_apk_path, alt_paths, system_file_path):
    """复制APK文件到目标目录，并在复制成功后删除源文件

    Args:
        build_job: 构建任务对象
        expected_apk_path: 预期APK路径
        alt_paths: 替代搜索路径列表
        system_file_path: 系统文件路径

    Returns:
        success: 是否成功复制文件
    """
    if not system_file_path:
        build_job.log += "警告: 未配置system_file_path，无法复制APK文件\n"
        return False

    try:
        # 获取公司和配置信息
        app_config = build_job.app_config
        company_name = app_config.company.name
        app_name = app_config.app_name

        # 构建目标路径: {system_file_path}/{company_name}/{app_name}/
        company_folder = "".join(
            c if c.isalnum() or c in ("_", "-", " ") else "_" for c in company_name
        ).strip()
        config_folder = "".join(
            c if c.isalnum() or c in ("_", "-", " ") else "_" for c in app_name
        ).strip()

        target_dir = os.path.join(system_file_path, company_folder, config_folder)
        build_job.log += f"目标目录: {target_dir}\n"

        # 确保目录存在
        os.makedirs(target_dir, exist_ok=True)

        # 从预期路径复制APK文件
        copied_files = _copy_apk_file_from_path(
            build_job, expected_apk_path, target_dir
        )

        # 如果在预期路径未找到APK，尝试替代路径
        if not copied_files:
            build_job.log += f"警告: 在预期路径中未找到APK文件: {expected_apk_path}\n"

            # 尝试查找其他可能的路径
            for alt_path in alt_paths:
                alt_copied_files = _copy_apk_file_from_path(
                    build_job, alt_path, target_dir
                )
                copied_files.extend(alt_copied_files)
                if alt_copied_files:
                    break  # 找到APK后停止查找

            if not copied_files:
                build_job.log += "警告: 未找到任何APK文件进行复制\n"
                return False

        # 如果复制了文件，记录输出路径
        build_job.output_file_path = target_dir
        build_job.log += f"最终输出路径: {target_dir}\n"
        return True

    except Exception as e:
        build_job.log += f"复制APK文件时发生错误: {str(e)}\n"
        logger.exception(f"复制APK文件时发生错误: {str(e)}")
        return False


def _get_app_name_pinyin(app_name):
    """
    将应用名称转换为拼音

    Args:
        app_name: 应用名称，如"萌电米多"

    Returns:
        str: 应用名称的拼音，如"mengdianmiduo"
    """
    try:
        # 尝试导入pypinyin库
        from pypinyin import lazy_pinyin, Style

        # 使用pypinyin库将中文转换为拼音
        # style=Style.NORMAL 表示使用普通风格（不带声调）
        pinyin_list = lazy_pinyin(app_name, style=Style.NORMAL)
        pinyin = "".join(pinyin_list)

        # 移除所有非字母数字字符
        pinyin = "".join(c for c in pinyin if c.isalnum())

        logger.info(f"应用名称 '{app_name}' 转换为拼音: '{pinyin}'")
    except ImportError:
        # 如果pypinyin库未安装，记录警告并使用简单的回退方案
        logger.warning(
            "pypinyin库未安装，无法准确转换中文到拼音。请使用 'uv add pypinyin' 安装。"
        )

        # 如果pypinyin库未安装，则返回None表示不进行转换
        logger.warning("pypinyin库未安装，无法转换中文到拼音。将保留原始文件名。")
        return None  # 返回None表示不进行文件名转换

    # 如果转换后为空，也返回None表示不进行转换
    if not pinyin:
        logger.warning(
            f"应用名称 '{app_name}' 无法转换为有效的拼音，将保留原始文件名。"
        )
        return None  # 返回None表示不进行文件名转换

    return pinyin


def _set_debug_info(build_job, project, app_type):
    debug_info = build_job.debug_info or ""
    debug_info += (
        f"\n[{build_job.created.strftime('%Y-%m-%d %H:%M:%S')}] ANDROID 阶段开始处理\n"
    )
    debug_info += f"APP类型: {app_type}\n"
    # 读取release.properties文件内容并添加到debug_info
    props_file_path = os.path.join(project.local_path, "release.properties")
    if os.path.exists(props_file_path):
        debug_info += "\n--- release.properties 配置文件内容 ---\n"
        try:
            with open(props_file_path, "r", encoding="utf-8") as f:
                props_content = f.read()
                debug_info += props_content + "\n"
            debug_info += "--- 配置文件结束 ---\n"
        except Exception as e:
            debug_info += f"读取配置文件失败: {str(e)}\n"
    else:
        debug_info += "\nrelease.properties 文件不存在\n"

    build_job.debug_info = debug_info
