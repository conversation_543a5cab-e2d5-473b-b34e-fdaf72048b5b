"""
服务器信息管理
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from ..models import ServerInfo

@admin.register(ServerInfo)
class ServerInfoAdmin(admin.ModelAdmin):
    """服务器信息管理类"""
    
    list_display = ('name', 'company', 'host', 'port', 'username', 'remote_path', 'is_default', 'last_used', 'created')
    list_filter = ('company', 'is_default')
    search_fields = ('name', 'host', 'company__name')
    readonly_fields = ('last_used', 'created', 'modified')
    ordering = ('company', '-is_default', 'name')
    
    fieldsets = (
        (None, {
            'fields': ('company', 'name', 'is_default')
        }),
        (_('服务器连接信息'), {
            'fields': ('host', 'port', 'username', 'password', 'remote_path')
        }),
        (_('其他信息'), {
            'fields': ('description', 'last_used', 'created', 'modified')
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """保存模型时的处理"""
        # 如果设置为默认，将同一公司的其他服务器设为非默认
        if obj.is_default:
            ServerInfo.objects.filter(company=obj.company).exclude(pk=obj.pk).update(is_default=False)
        super().save_model(request, obj, form, change)
