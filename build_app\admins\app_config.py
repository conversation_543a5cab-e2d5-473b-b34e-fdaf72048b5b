"""
应用配置管理类
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _


class AppConfigAdmin(admin.ModelAdmin):
    """应用配置管理类"""
    list_display = ('config_name', 'company', 'app_name', 'application_id', 'invite_code', 'keystore_base_name', 'created')
    list_filter = ('company', 'created', 'cocos_ad_platform')
    search_fields = ('config_name', 'company__name', 'app_name', 'base_url', 'application_id', 'keystore_base_name')
    readonly_fields = ('created', 'modified', 'android_use_csj_sdk', 'is_ak')
    save_as = True  # 启用「另存为新」功能，提供复制编辑能力
    save_as_continue = True  # 保存为新对象后继续编辑新对象

    fieldsets = (
        (None, {
            'fields': (
                'company', 'config_name', 'base_url',
                'app_name','application_id', 'cocos_ad_platform',
                'version_code_base',
            )
        }),
        (_('常用配置'), {
            'fields': (
                'baidu_location_ak',
                'android_icon',
                'keystore_base_name',
                'keystore_file',
                'invite_code',
                'cocos_login_type',
                'weixin_app_id',
                'weixin_app_secret',
            )
        }),
        (_('高级/技术配置'), {
            'fields': ('debug_log', 'android_hide_icon', 'extra_data', 'injection_rules'),
            'classes': ('collapse',),
        }),
        (_('时间戳'), {
            'fields': ('created', 'modified'),
            'classes': ('collapse',),
        }),
    )

    def get_queryset(self, request):
        """重写queryset以优化查询"""
        return super().get_queryset(request).select_related('company')
