"""
公司管理类
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _


class CompanyAdmin(admin.ModelAdmin):
    """公司管理类"""
    list_display = ('name', 'cooperation_type', 'is_active', 'updated_at')
    search_fields = ('name', 'cooperation_type', 'aggregation_platform')
    list_filter = ('is_active', 'cooperation_type')
    readonly_fields = ('created_at', 'updated_at')
    save_as = True  # 启用「另存为新」功能，提供复制编辑能力
    save_as_continue = True  # 保存为新对象后继续编辑新对象
