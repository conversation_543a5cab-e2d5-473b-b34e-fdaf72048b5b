"""
上传记录管理类
"""
import datetime
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.safestring import mark_safe
from django.http import HttpResponse


class UploadedFileAdmin(admin.ModelAdmin):
    """上传文件记录管理类"""
    list_display = ('company_name', 'project_name', 'app_name', 
                    'download_link', 'download_link_display', 'created')
    search_fields = ('company_name', 'project_name', 'app_name', 'file_name')
    list_filter = ('company_name', 'created')
    readonly_fields = ('created', 'modified')
    ordering = ('-created',)
    actions = ['export_as_txt']

    # 禁用所有常规的CRUD操作
    def has_add_permission(self, request):
        """禁止手动添加上传记录"""
        return False
    
    def file_size_display(self, obj):
        """显示更友好的文件大小"""
        size = obj.file_size
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size/1024:.2f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size/(1024*1024):.2f} MB"
        else:
            return f"{size/(1024*1024*1024):.2f} GB"
    file_size_display.short_description = _("文件大小")
    
    def download_link_display(self, obj):
        """显示可点击的下载链接"""
        if obj.download_link:
            return mark_safe(f'<a href="{obj.download_link}" target="_blank">下载</a>')
        return "-"
    download_link_display.short_description = _("下载链接")
    download_link_display.allow_tags = True
    
    def export_as_txt(self, request, queryset):
        """将选中的记录导出为.txt文件，只包含应用名称和下载链接"""
        # 生成文件名称，包含当前时间
        timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        filename = f"download_links_{timestamp}.txt"
        
        # 创建响应对象
        response = HttpResponse(content_type='text/plain')
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        # 导出数据
        for record in queryset:
            if record.download_link:  # 只导出有效链接
                # 格式：应用名称 下载链接
                line = f"{record.app_name} {record.download_link}\n"
                response.write(line)
        
        self.message_user(request, f"已导出 {queryset.count()} 条记录到 {filename}")
        return response
    export_as_txt.short_description = _("导出选中项为文本文件")
