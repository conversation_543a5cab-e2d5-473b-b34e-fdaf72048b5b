"""
Admin视图模块 - 提供在Django Admin中集成的自定义视图
"""

import os
import mimetypes
from pathlib import Path
import shutil
import logging

from django.shortcuts import render, get_object_or_404, redirect
from django.http import FileResponse, JsonResponse, Http404
from django.contrib.admin.views.decorators import staff_member_required
from django.urls import reverse
from django.conf import settings
from django.utils.text import slugify
from django.contrib import messages, admin
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

from .models import Company, AppConfig

logger = logging.getLogger(__name__)


@staff_member_required
def file_manager_admin_view(request):
    """文件管理主页 - 在Admin界面中显示"""
    # 获取搜索参数
    search_query = request.GET.get("search", "")

    # 获取当前页码
    page = request.GET.get("page", 1)

    # 每页显示的公司数量
    per_page = 12  # 每页显示12个公司，适合网格布局

    # 构建查询
    companies_queryset = Company.objects.filter(is_active=True)

    # 如果有搜索关键词，进行模糊查询
    if search_query:
        companies_queryset = companies_queryset.filter(name__icontains=search_query)

    # 排序结果
    companies_queryset = companies_queryset.order_by("name")

    # 创建分页器
    paginator = Paginator(companies_queryset, per_page)

    try:
        # 获取当前页的公司列表
        companies_page = paginator.page(page)
    except PageNotAnInteger:
        # 如果页码不是整数，返回第一页
        companies_page = paginator.page(1)
    except EmptyPage:
        # 如果页码超出范围，返回最后一页
        companies_page = paginator.page(paginator.num_pages)

    context = {
        "title": "文件管理",
        "companies": companies_page,  # 分页后的结果
        "search_query": search_query,
        "total_companies": paginator.count,  # 总公司数
        "page_obj": companies_page,  # 分页对象，用于模板中生成分页控件
        "paginator": paginator,
    }

    # 为Admin添加额外context
    context.update(admin.site.each_context(request))

    return render(request, "admin/file_manager/index.html", context)


@staff_member_required
def company_files_admin_view(request, company_id):
    """显示特定公司的应用配置列表 - Admin视图"""
    company = get_object_or_404(Company, pk=company_id)
    app_configs = AppConfig.objects.filter(company=company, is_active=True).order_by(
        "config_name"
    )

    context = {
        "title": f"{company.name} - 文件管理",
        "company": company,
        "app_configs": app_configs,
    }

    # 为Admin添加额外context
    context.update(admin.site.each_context(request))

    return render(request, "admin/file_manager/company_detail.html", context)


@staff_member_required
def app_files_admin_view(request, app_config_id):
    """显示特定应用配置的文件列表 - Admin视图"""
    app_config = get_object_or_404(AppConfig, pk=app_config_id)

    # 获取应用媒体目录的路径
    media_dir = app_config.app_media_base_dir

    # 检查目录是否存在
    if not media_dir.exists():
        messages.warning(request, f"目录 {media_dir} 不存在")
        files = []
    else:
        # 获取文件列表
        files = []
        for path in media_dir.glob("**/*"):
            if path.is_file():
                # 计算相对于 media_dir 的路径
                rel_path = path.relative_to(media_dir)

                # 过滤包含'res-'的文件夹中的文件
                # 兼容Windows和Linux的路径分隔符
                path_parts = str(rel_path).replace("\\", "/").split("/")
                if any(part.startswith("res-") for part in path_parts):
                    continue

                # 获取文件大小
                size_bytes = path.stat().st_size
                size_kb = size_bytes / 1024
                size_mb = size_kb / 1024

                if size_mb >= 1:
                    size_str = f"{size_mb:.2f} MB"
                else:
                    size_str = f"{size_kb:.2f} KB"

                # 获取修改时间
                mod_time = path.stat().st_mtime

                files.append(
                    {
                        "path": str(rel_path),
                        "full_path": str(path),
                        "name": path.name,
                        "size": size_str,
                        "size_bytes": size_bytes,
                        "modified": mod_time,
                        "is_image": path.suffix.lower()
                        in [".jpg", ".jpeg", ".png", ".gif", ".bmp"],
                    }
                )

    # 按照修改时间排序，最新的在前
    files.sort(key=lambda x: x["modified"], reverse=True)

    # 获取公司信息
    company = app_config.company

    # 获取服务器信息
    server_ip, server_password, server_port, server_username, remote_path = _get_server_info(company)

    # 获取下载链接并格式化
    download_link = _get_download_link(
        company.download_link, app_config.base_url
    )

    context = {
        "title": f"{app_config.config_name} - 文件管理",
        "app_config": app_config,
        "files": files,
        "media_dir": str(media_dir),
        # 服务器信息
        "server_ip": server_ip,
        "server_password": server_password,
        "server_port": server_port,
        "server_username": server_username,
        "remote_path": remote_path,
        "download_link": download_link,
    }

    # 为Admin添加额外context
    context.update(admin.site.each_context(request))

    return render(request, "admin/file_manager/app_files.html", context)


@staff_member_required
def download_file_admin_view(request, app_config_id, path):
    """下载特定应用配置文件 - Admin视图"""
    app_config = get_object_or_404(AppConfig, pk=app_config_id)
    media_dir = app_config.app_media_base_dir

    # 确保路径安全（防止目录遍历攻击）
    try:
        # 把路径转换为 Path 对象，然后确保这个路径是 media_dir 的子路径
        requested_path = (media_dir / path).resolve()
        if not str(requested_path).startswith(str(media_dir.resolve())):
            messages.error(request, "非法的文件路径请求")
            return redirect("admin:file_manager_app", app_config_id=app_config_id)

        if not requested_path.exists() or not requested_path.is_file():
            messages.error(request, f"文件 {path} 不存在")
            return redirect("admin:file_manager_app", app_config_id=app_config_id)

        # 确定MIME类型
        content_type, _ = mimetypes.guess_type(str(requested_path))
        content_type = content_type or "application/octet-stream"

        # 创建响应
        response = FileResponse(open(requested_path, "rb"), content_type=content_type)
        response["Content-Disposition"] = (
            f'attachment; filename="{requested_path.name}"'
        )
        return response

    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        messages.error(request, f"文件下载失败: {str(e)}")
        return redirect("admin:file_manager_app", app_config_id=app_config_id)


@staff_member_required
def delete_file_admin_view(request, app_config_id, path):
    """删除特定应用配置文件 - Admin视图"""
    if request.method != "POST":
        return JsonResponse({"success": False, "error": "只允许POST请求"})

    app_config = get_object_or_404(AppConfig, pk=app_config_id)
    media_dir = app_config.app_media_base_dir

    # 确保路径安全
    try:
        requested_path = (media_dir / path).resolve()
        if not str(requested_path).startswith(str(media_dir.resolve())):
            return JsonResponse({"success": False, "error": "非法的文件路径请求"})

        if not requested_path.exists() or not requested_path.is_file():
            return JsonResponse({"success": False, "error": f"文件 {path} 不存在"})

        # 删除文件
        os.remove(requested_path)

        # 如果文件所在目录为空，则也删除目录
        parent_dir = requested_path.parent
        if parent_dir != media_dir and not any(parent_dir.iterdir()):
            parent_dir.rmdir()

        return JsonResponse({"success": True})

    except Exception as e:
        logger.error(f"文件删除失败: {str(e)}")
        return JsonResponse({"success": False, "error": str(e)})


@staff_member_required
def upload_file_admin_view(request, app_config_id):
    """上传文件到特定应用配置目录 - Admin视图"""
    if request.method != "POST":
        return JsonResponse({"success": False, "error": "只允许POST请求"})

    app_config = get_object_or_404(AppConfig, pk=app_config_id)
    media_dir = app_config.app_media_base_dir

    try:
        # 确保目录存在
        media_dir.mkdir(parents=True, exist_ok=True)

        # 获取上传的文件
        uploaded_file = request.FILES.get("file")
        if not uploaded_file:
            return JsonResponse({"success": False, "error": "未接收到文件"})

        # 确定文件存储路径
        subdir = request.POST.get("subdir", "")
        if subdir:
            # 确保子目录是安全的
            target_dir = (media_dir / subdir).resolve()
            if not str(target_dir).startswith(str(media_dir.resolve())):
                return JsonResponse({"success": False, "error": "非法的目录路径"})
            target_dir.mkdir(parents=True, exist_ok=True)
        else:
            target_dir = media_dir

        # 保存文件
        file_path = target_dir / uploaded_file.name
        with open(file_path, "wb+") as destination:
            for chunk in uploaded_file.chunks():
                destination.write(chunk)

        return JsonResponse(
            {
                "success": True,
                "file_name": uploaded_file.name,
                "file_path": str(file_path.relative_to(media_dir)),
                "file_size": file_path.stat().st_size,
            }
        )

    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        return JsonResponse({"success": False, "error": str(e)})


def create_folder_admin_view(request, app_config_id):
    """在特定应用配置目录中创建文件夹 - Admin视图"""
    if request.method != "POST":
        return JsonResponse({"success": False, "error": "只允许POST请求"})

    app_config = get_object_or_404(AppConfig, pk=app_config_id)
    media_dir = app_config.app_media_base_dir

    try:
        folder_name = request.POST.get("folder_name", "").strip()
        parent_dir = request.POST.get("parent_dir", "").strip()

        if not folder_name:
            return JsonResponse({"success": False, "error": "文件夹名称不能为空"})

        # 构建完整路径
        if parent_dir:
            new_folder_path = (media_dir / parent_dir / folder_name).resolve()
            # 安全检查
            if not str(new_folder_path).startswith(str(media_dir.resolve())):
                return JsonResponse({"success": False, "error": "非法的目录路径"})
        else:
            new_folder_path = (media_dir / folder_name).resolve()

        # 创建文件夹
        new_folder_path.mkdir(parents=True, exist_ok=True)

        return JsonResponse(
            {
                "success": True,
                "folder_name": folder_name,
                "folder_path": str(new_folder_path.relative_to(media_dir)),
            }
        )

    except Exception as e:
        logger.error(f"创建文件夹失败: {str(e)}")
        return JsonResponse({"success": False, "error": str(e)})


def _get_server_info(company):
    """
    获取公司的服务器信息
    首先从 ServerInfo 模型获取，如果没有再从 company.server_info 文本字段解析
    """
    server_ip = ""
    server_password = ""
    server_port = 22
    server_username = "root"
    remote_path = "/www/wwwroot/download"
    
    try:
        # 从 ServerInfo 模型获取信息
        from .models import ServerInfo
        
        # 先查找默认服务器
        server = ServerInfo.objects.filter(company=company, is_default=True).first()
        
        # 如果没有默认服务器，获取最近使用的服务器
        if not server:
            server = ServerInfo.objects.filter(company=company).order_by('-last_used').first()
        
        # 如果找到服务器信息，使用它
        if server:
            server_ip = server.host
            server_password = server.password
            server_port = server.port
            server_username = server.username
            remote_path = server.remote_path
            return server_ip, server_password, server_port, server_username, remote_path
    except Exception as e:
        import logging
        logging.error(f"从 ServerInfo 获取服务器信息失败: {e}")
    
    # 如果没有从数据库获取到信息，则尝试从 company.server_info 字段解析
    server_info = company.server_info or ""
    
    if server_info:
        import re

        # 使用正则表达式匹配服务器IP
        ip_pattern = r"服务器ip[：:]\s*([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})"
        ip_match = re.search(ip_pattern, server_info)
        server_ip = ip_match.group(1) if ip_match else ""

        # 使用正则表达式匹配服务器密码
        password_pattern = r"服务器密码[：:]\s*([^，,\n]*)"
        password_match = re.search(password_pattern, server_info)
        server_password = password_match.group(1).strip() if password_match else ""
        
        # 尝试匹配端口、用户名和路径
        port_pattern = r"端口[：:]\s*([0-9]+)"
        port_match = re.search(port_pattern, server_info)
        server_port = int(port_match.group(1)) if port_match else 22
        
        username_pattern = r"用户名[：:]\s*([^，,\n]*)"
        username_match = re.search(username_pattern, server_info)
        server_username = username_match.group(1).strip() if username_match else "root"
        
        path_pattern = r"路径[：:]\s*([^，,\n]*)"
        path_match = re.search(path_pattern, server_info)
        remote_path = path_match.group(1).strip() if path_match else "/www/wwwroot/download"
    
    return server_ip, server_password, server_port, server_username, remote_path


def _get_download_link(o_url,base_url):
    """
    将URL格式从https://yixiaf2.yizhuo.xin/转换为http://download.yizhuo.xin

    Args:
        o_url: 原始URL字符串
    Args:
        base: 基础URL字符串

    Returns:
        str: 转换后的URL字符串
    """
    if o_url:
      url = o_url

    import re

    # 获取base_url
    base_url = base_url or ""
    # 匹配URL模式
    pattern = r"https?://([^.]+)\.([^/]+\.[^/]+)/?.*"
    match = re.match(pattern, base_url)

    if match:
        # 提取域名的主要部分（如yizhuo.xin）
        domain = match.group(2)
        # 构建新的URL
        return f"http://download.{domain}"

    return url  # 如果不匹配模式，返回原始URL
