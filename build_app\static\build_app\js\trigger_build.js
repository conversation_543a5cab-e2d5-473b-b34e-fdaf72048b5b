// Ensure this code runs after the DOM is ready and jQuery is available
$(document).ready(function () {
    // Get the form element (we'll read data attributes from it)
    const form = $('#trigger-build-form'); 
    // Read URLs from data attributes
    const projectsUrl = form.data('projects-url'); 
    const configsUrl = form.data('configs-url');

    const companySelect = $('#company-select');
    const projectSelect = $('#project-select');
    const appConfigListDiv = $('#app-config-list');
    const submitButton = $('#submit-button');
    const submitHelp = $('#submit-help');
    const hiddenConfigIdsInput = $('#app-config-ids-hidden');
    const branchInput = $('#branch-input');

    // Initialize Select2 for the company dropdown
    companySelect.select2({
        placeholder: "请搜索或选择公司", // Add a placeholder
        allowClear: true, // Optional: adds a clear button
        // theme: "bootstrap4" // Optional: if using Bootstrap theme CSS
        "min-width": '350px' // Use the existing style attribute for width, or set explicitly e.g., '100%'
    });

    // --- Helper Functions ---
    function updateSubmitButtonState() {
        const selectedConfigs = appConfigListDiv.find('input[type="checkbox"]:checked');
        const projectSelected = projectSelect.val(); // Check if a project is selected

        if (selectedConfigs.length > 0 && projectSelected) {
            submitButton.prop('disabled', false);
            submitHelp.hide();
        } else {
            submitButton.prop('disabled', true);
            let helpText = [];
            if (!projectSelected) {
                helpText.push('请选择项目');
            }
            if (selectedConfigs.length === 0) {
                helpText.push('请至少选择一个应用配置');
            }
            submitHelp.text(helpText.join('，')).show();
        }
    }

    function loadProjects() {
        appConfigListDiv.html('<div>--- 请先选择公司 ---</div>'); // Clear configs too
        projectSelect.empty().append('<option value="">--- 正在加载项目 ---</option>').prop('disabled', true);
        hiddenConfigIdsInput.val(''); // Clear hidden input
        updateSubmitButtonState(); // Update button state immediately

        // Fetch ALL projects (no company filter)
        $.ajax({
            url: projectsUrl, // Use URL from data attribute
            dataType: 'json',
            success: function (data) {
                projectSelect.empty().append('<option value="">--- 请选择项目 ---</option>'); // Reset placeholder
                if (data.projects && data.projects.length > 0) {
                    $.each(data.projects, function (key, value) {
                        projectSelect.append($('<option></option>').attr('value', value.id).text(value.name));
                    });
                    projectSelect.prop('disabled', false); // Enable project select
                } else {
                    projectSelect.append('<option value="">--- 无可用项目 ---</option>'); // Keep it disabled if none
                }
                updateSubmitButtonState(); // Update based on project selection status
            },
            error: function () {
                projectSelect.empty().append('<option value="">--- 加载项目失败 ---</option>');
                // Consider more user-friendly error display
            }
        });
    }

    function loadConfigs(companyId) {
        appConfigListDiv.html('<div>--- 正在加载配置 ---</div>');
        hiddenConfigIdsInput.val(''); // Clear hidden input
        updateSubmitButtonState(); // Update button state immediately

        $.ajax({
            url: configsUrl, // Use URL from data attribute
            data: {
                'company_id': companyId
            },
            dataType: 'json',
            success: function (data) {
                appConfigListDiv.empty(); // Clear loading message
                if (data.configs && data.configs.length > 0) {
                    $.each(data.configs, function (key, value) {
                        const checkboxId = 'config-' + value.id;
                        const checkbox = $('<input type="checkbox" name="app_configs" value="' + value.id + '" id="' + checkboxId + '" class="config-checkbox">');
                        const label = $('<label for="' + checkboxId + '" style="margin-left: 5px;"></label>').text(value.name);
                        appConfigListDiv.append($('<div style="display:flex;"></div>').append(checkbox).append(label));
                    });
                } else {
                    appConfigListDiv.html('<div>--- 无可用应用配置 ---</div>');
                }
                // Attach change handler after adding checkboxes
                $('.config-checkbox').change(updateHiddenInputAndButton);
                updateSubmitButtonState();
            },
            error: function () {
                appConfigListDiv.html('<div>--- 加载配置失败 ---</div>');
                // Consider more user-friendly error display
                updateSubmitButtonState();
            }
        });
    }

    function updateHiddenInputAndButton() {
        const selectedIds = [];
        appConfigListDiv.find('input[type="checkbox"]:checked').each(function() {
            selectedIds.push($(this).val());
        });
        hiddenConfigIdsInput.val(selectedIds.join(','));
        updateSubmitButtonState(); // Also update the button whenever selection changes
    }

    // --- Event Listeners ---
    companySelect.change(function () {
        const companyId = $(this).val();
        projectSelect.empty().append('<option value="">--- 请先选择公司 ---</option>').prop('disabled', true);
        appConfigListDiv.html('<div>--- 请先选择公司 ---</div>');
        hiddenConfigIdsInput.val(''); // Clear hidden input

        if (companyId) {
            // Load both projects and configs when company changes
            loadProjects(); // Fetches all projects
            loadConfigs(companyId); // Fetches configs for this company
        } else {
            // Reset if no company is selected
            updateSubmitButtonState();
        }
    });

    // Update button state when project selection changes
    projectSelect.change(function() {
        updateSubmitButtonState();
    });

    // Update hidden input and button state when checkboxes change
    // Use event delegation on the container
    appConfigListDiv.on('change', '.config-checkbox', updateHiddenInputAndButton);

    // Initial state check
    updateSubmitButtonState();

    // 添加表单提交监听器，防止重复提交
    form.submit(function(event) {
        // 禁用提交按钮并更改文本
        submitButton.prop('disabled', true);
        submitButton.val('构建中...');
        
        // 添加一个加载提示
        submitHelp.text('正在创建构建任务，请稍候...').show();
        
        // 允许表单继续提交
        return true;
    });

});
