# Generated by Django 4.2.20 on 2025-05-29 02:15

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields


class Migration(migrations.Migration):

    dependencies = [
        ('build_app', '0027_uploadedfile'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServerInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('name', models.CharField(help_text='用于标识的服务器名称', max_length=100, verbose_name='服务器名称')),
                ('host', models.CharField(help_text='服务器IP地址或域名', max_length=255, verbose_name='服务器地址')),
                ('port', models.IntegerField(default=22, help_text='SSH端口号，默认为22', verbose_name='服务器端口')),
                ('username', models.CharField(help_text='登录用户名', max_length=100, verbose_name='用户名')),
                ('password', models.CharField(help_text='登录密码', max_length=255, verbose_name='密码')),
                ('remote_path', models.CharField(help_text='文件上传的远程目录路径', max_length=500, verbose_name='远程路径')),
                ('is_default', models.BooleanField(default=False, help_text='是否为该公司的默认服务器', verbose_name='是否默认')),
                ('description', models.TextField(blank=True, help_text='服务器用途描述', null=True, verbose_name='描述')),
                ('last_used', models.DateTimeField(blank=True, help_text='最后一次上传文件的时间', null=True, verbose_name='最后使用时间')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='server_infos', to='build_app.company', verbose_name='所属公司')),
            ],
            options={
                'verbose_name': '服务器信息',
                'verbose_name_plural': '服务器信息',
                'db_table': 'server_infos',
                'ordering': ['company', '-is_default', 'name'],
                'unique_together': {('company', 'host', 'port', 'username')},
            },
        ),
    ]
