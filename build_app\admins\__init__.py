"""
管理界面模块 - 导入和注册所有Admin类
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

# 导入所有Admin类
from .system_config import SystemConfigAdmin
from .company import CompanyAdmin
from .project import ProjectAdmin
from .app_config import AppConfigAdmin
from .build_job import BuildJobAdmin
from .uploaded_file import UploadedFileAdmin
from .file_management import FileManagement, FileManagementAdmin
from .log_entry import LogEntryAdmin
from .server_info import ServerInfoAdmin

# 导入相关模型
from ..models import (
    SystemConfig, Company, Project, 
    AppConfig, BuildJob, UploadedFile, ServerInfo
)
from django.contrib.admin.models import LogEntry

# 设置管理界面标题
admin.site.site_header = _("自动化构建系统")

# 注册所有模型和对应的Admin类
admin.site.register(SystemConfig, SystemConfigAdmin)
admin.site.register(Company, CompanyAdmin)
admin.site.register(Project, ProjectAdmin)
admin.site.register(AppConfig, AppConfigAdmin)
admin.site.register(BuildJob, BuildJobAdmin)
admin.site.register(UploadedFile, UploadedFileAdmin)
admin.site.register(FileManagement, FileManagementAdmin)
admin.site.register(LogEntry, LogEntryAdmin)
