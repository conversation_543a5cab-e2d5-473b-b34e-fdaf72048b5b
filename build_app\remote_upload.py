"""
远程文件上传模块 - 提供将文件上传到远程服务器的功能
"""
import os
import logging
import paramiko
from pathlib import Path
from django.http import JsonResponse
from django.contrib.admin.views.decorators import staff_member_required
from django.shortcuts import get_object_or_404
from .models import AppConfig

logger = logging.getLogger(__name__)

@staff_member_required
def upload_to_remote_server(request, app_config_id, path):
    """
    将文件上传到远程服务器

    Args:
        request: HTTP请求对象
        app_config_id: 应用配置ID
        path: 要上传的文件路径（相对于应用媒体目录）

    Returns:
        JsonResponse: 包含上传结果的JSON响应
    """
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '只允许POST请求'})

    # 获取应用配置
    app_config = get_object_or_404(AppConfig, pk=app_config_id)
    media_dir = app_config.app_media_base_dir

    # 确保路径安全
    try:
        # 构建完整的本地文件路径
        local_file_path = (media_dir / path).resolve()
        if not str(local_file_path).startswith(str(media_dir.resolve())):
            return JsonResponse({'success': False, 'error': "非法的文件路径请求"})

        if not local_file_path.exists() or not local_file_path.is_file():
            return JsonResponse({'success': False, 'error': f"文件 {path} 不存在"})

        # 检查文件是否为APK
        if not local_file_path.suffix.lower() == '.apk':
            return JsonResponse({'success': False, 'error': "只能上传APK文件"})

        # 获取远程服务器信息
        server_host = request.POST.get('server_host')
        server_port = int(request.POST.get('server_port', 22))
        server_username = request.POST.get('server_username')
        server_password = request.POST.get('server_password')
        remote_path = request.POST.get('remote_path')

        # 验证必要的参数
        if not all([server_host, server_username, server_password, remote_path]):
            return JsonResponse({'success': False, 'error': "服务器信息不完整"})

        # 使用Paramiko通过SFTP上传文件
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        try:
            # 连接到远程服务器
            ssh_client.connect(
                hostname=server_host,
                port=server_port,
                username=server_username,
                password=server_password,
                timeout=10
            )

            # 创建SFTP客户端
            sftp_client = ssh_client.open_sftp()

            # 确保远程目录存在
            try:
                sftp_client.stat(remote_path)
            except FileNotFoundError:
                # 创建远程目录
                command = f"mkdir -p {remote_path}"
                stdin, stdout, stderr = ssh_client.exec_command(command)
                exit_status = stdout.channel.recv_exit_status()
                if exit_status != 0:
                    error_msg = stderr.read().decode('utf-8')
                    return JsonResponse({'success': False, 'error': f"创建远程目录失败: {error_msg}"})

            # 上传文件
            remote_file_path = f"{remote_path}/{local_file_path.name}"
            sftp_client.put(str(local_file_path), remote_file_path)

            # 关闭连接
            sftp_client.close()
            ssh_client.close()
            
            # 保存服务器信息到数据库
            try:
                from .models import ServerInfo
                from django.utils import timezone
                
                # 检查该公司是否已经有相同的服务器信息
                company = app_config.company
                server_exists = ServerInfo.objects.filter(
                    company=company,
                    host=server_host,
                    port=server_port,
                    username=server_username
                ).exists()
                
                # 如果不存在，则创建新的服务器信息记录
                if not server_exists:
                    ServerInfo.objects.create(
                        company=company,
                        name=f"{server_host}-{server_username}",  # 默认名称
                        host=server_host,
                        port=server_port,
                        username=server_username,
                        password=server_password,
                        remote_path=remote_path,
                        is_default=not ServerInfo.objects.filter(company=company).exists(),  # 如果是该公司的第一个服务器，则设为默认
                        description=f"通过APK上传功能自动添加 - {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
                        last_used=timezone.now()
                    )
                    logger.info(f"已保存服务器信息: {server_host}")
                else:
                    # 更新已有服务器信息的最后使用时间
                    ServerInfo.objects.filter(
                        company=company,
                        host=server_host,
                        port=server_port,
                        username=server_username
                    ).update(
                        last_used=timezone.now(),
                        remote_path=remote_path  # 更新远程路径
                    )
                    logger.info(f"已更新服务器信息: {server_host}")
            except Exception as e:
                logger.error(f"保存服务器信息失败: {e}")
            
            # 保存上传记录
            try:
                from .models import UploadedFile, Project, BuildJob
                from .admin_views import _get_download_link
                
                # 获取项目信息
                # 正确的查询方式：先获取AppConfig，然后通过它获取Project
                project_name = "未知项目"
                try:
                    # 直接从构建任务中找到项目信息
                    build_job = BuildJob.objects.filter(app_config_id=app_config_id).first()
                    if build_job and build_job.project:
                        project_name = build_job.project.name
                    # 如果没有相关的构建任务，使用配置名称
                    else:
                        project_name = app_config.config_name
                except Exception as e:
                    logger.warning(f"获取项目信息时出错: {e}")
                    project_name = app_config.config_name
                
                # 构建下载链接
                company = app_config.company
                download_link = _get_download_link(company.download_link, app_config.base_url)
                
                # 如果有效，添加文件名
                if download_link:
                    download_link = f"{download_link}/{local_file_path.name}"
                
                # 计算文件大小
                file_size = local_file_path.stat().st_size
                
                # 创建记录
                UploadedFile.objects.create(
                    company_name=company.name,
                    project_name=project_name,
                    config_name=app_config.config_name,
                    app_name=app_config.app_name,
                    file_name=local_file_path.name,
                    file_size=file_size,
                    download_link=download_link or "",
                    remote_path=remote_file_path,
                    remote_server=server_host
                )
                
                logger.info(f"已保存上传记录: {app_config.app_name} - {local_file_path.name}")
            except Exception as e:
                logger.error(f"保存上传记录失败: {e}")
            
            return JsonResponse({
                'success': True,
                'message': f"文件已成功上传到 {server_host}:{remote_file_path}"
            })

        except Exception as e:
            logger.error(f"上传文件到远程服务器失败: {str(e)}")
            return JsonResponse({'success': False, 'error': f"上传失败: {str(e)}"})

    except Exception as e:
        logger.error(f"处理远程上传请求时发生错误: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})
