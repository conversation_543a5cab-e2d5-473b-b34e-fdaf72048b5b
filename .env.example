# .env.example (Template for environment variables)

# Django Settings
DJANGO_SECRET_KEY='your_very_secret_random_key_here' # Replace with a strong random key
DJANGO_DEBUG=True # Set to False in production
DJANGO_ALLOWED_HOSTS=localhost # Add your production domain(s) here

# Database Settings (Uncomment and configure for PostgreSQL)
# DATABASE_URL=postgresql://user:password@host:port/dbname
# OR use individual variables:
# DB_NAME=your_db_name
# DB_USER=your_db_user
# DB_PASSWORD=your_db_password
# DB_HOST=localhost
# DB_PORT=5432

# Celery Settings (Using Redis)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Other API Keys/Secrets (Add as needed)
# GITEE_ACCESS_TOKEN=your_gitee_token
# COCOS_CREATOR_PATH=C:/Path/To/CocosCreator.exe
# ANDROID_SDK_PATH=C:/Path/To/Android/Sdk
# etc.
