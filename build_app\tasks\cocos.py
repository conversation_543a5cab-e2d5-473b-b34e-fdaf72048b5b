"""
Cocos 构建模块，负责 Cocos2d 项目的构建流程。
"""
import os
import logging
from pathlib import Path
import shutil

from celery import shared_task
from django.conf import settings

from ..models import BuildJob, AppConfig
from .base import run_command, get_system_config, check_job_canceled, run_command_with_cancel_support

logger = logging.getLogger(__name__)

from .lock import acquire_project_lock, release_project_lock

@shared_task(bind=True, max_retries=2)
def build_cocos_project(self, build_job_id):
    """构建 Cocos 项目。
    
    简化版，默认只支持 Android 平台和 release 模式的 Cocos 项目构建。
    采用模块化设计，将构建过程分解为多个单一职责的辅助函数。
    
    Args:
        build_job_id: 构建任务 ID
        
    Returns:
        执行结果消息
    """
    build_job = None
    project_lock_acquired = False
    
    try:
        # --- 获取构建任务 ---
        build_job = BuildJob.objects.get(pk=build_job_id)
        project = build_job.project
        app_config = build_job.app_config
        
        # --- 获取项目锁，确保同一个项目的任务不会并行执行 ---
        if not acquire_project_lock(project.id):
            error_msg = f"无法获取项目锁，请稍后重试。项目 ID: {project.id}"
            logger.error(error_msg)
            build_job.log += f"\n{error_msg}\n"
            build_job.status = BuildJob.Status.FAILED
            build_job.save(update_fields=['status', 'log'])
            return error_msg
            
        project_lock_acquired = True
        
        logger.info(f"开始 Cocos 项目构建，任务 ID: {build_job_id}")
        
        # --- 更新构建任务状态为 BUILDING ---
        build_job.status = BuildJob.Status.BUILDING
        build_job.current_step = BuildJob.BuildStep.COCOS
        build_job.log += f"\n========== COCOS 构建阶段 ==========\n"
        build_job.log += f"时间: {build_job.created.strftime('%Y-%m-%d %H:%M:%S')}\n"
        build_job.save(update_fields=['status', 'current_step', 'log'])
        
        # --- COCOS 配置注入 ---
        build_job.log += f"\n正在为 Cocos 构建注入配置...\n"
        build_job.save(update_fields=['log'])
        
        try:
            # 生成 Cocos 配置文件 (buildConfig.ts)
            cocos_config_path = os.path.join(project.local_path, 'assets', 'buildConfig.ts')
            
            from ..utils import generate_cocos_config
            # 使用定制函数生成 Cocos 配置
            config_content = generate_cocos_config(app_config, build_job)
            
            # 写入配置文件
            os.makedirs(os.path.dirname(cocos_config_path), exist_ok=True)
            with open(cocos_config_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            # 记录配置文件位置到日志
            build_job.log += f"\n成功生成 Cocos 配置文件: {cocos_config_path}\n"

            # 将配置内容写入调试日志
            debug_info = build_job.debug_info or ""
            debug_info += f"\n--- Cocos 配置内容 ---\n"
            debug_info += f"{config_content}\n"
            debug_info += f"--- 配置内容结束 ---\n\n"
            build_job.debug_info = debug_info
            
            build_job.save(update_fields=['log', 'debug_info'])
            
        except Exception as e:
            error_msg = f"注入 Cocos 配置失败: {e}"
            logger.error(error_msg)
            build_job.log += f"\n{error_msg}\n"
            build_job.status = BuildJob.Status.FAILED
            build_job.save(update_fields=['status', 'log'])
            return error_msg
        
        # --- 继续检查当前任务状态 ---
        if build_job.status == BuildJob.Status.FAILED:
            # 如果任务已经失败，不再继续
            error_msg = f"任务状态为 FAILED，不继续执行"
            logger.error(error_msg)
            build_job.log += f"\n{error_msg}\n"
            build_job.save(update_fields=['log'])
            return error_msg
            
        # --- 检查当前任务步骤，确保是 Cocos 阶段 ---
        if build_job.current_step != BuildJob.BuildStep.COCOS:
            # 非法的步骤跳转，可能是由于并发任务造成的
            error_msg = f"错误: 当前任务步骤({build_job.current_step})不是COCOS阶段，拒绝执行"
            logger.error(error_msg)
            build_job.log += f"\n{error_msg}\n"
            build_job.status = BuildJob.Status.FAILED
            build_job.save(update_fields=['status', 'log'])
            return error_msg
            
        # --- 构建调试日志 ---
        debug_info = build_job.debug_info or ""
        debug_info += f"\n[{build_job.created.strftime('%Y-%m-%d %H:%M:%S')}] COCOS 阶段开始处理\n"
        build_job.debug_info = debug_info
        
        # --- 更新任务状态 ---
        build_job.status = BuildJob.Status.BUILDING
        build_job.log += f"开始 Cocos 项目构建 (Task #{build_job_id} Step: {build_job.current_step})...\n"
        build_job.save(update_fields=['status', 'log', 'debug_info'])
        
        # --- 获取路径和配置 ---
        project_path, cocos_creator_path = get_base_path(build_job, project)
        
        # --- 准备构建参数 ---
        build_args, safe_output_name, cocos_build_name = get_build_args(build_job)
        
        # --- 执行 Cocos 构建 ---
        # 如果构建失败，run_cocos_command 会抛出异常
        run_cocos_command(build_job, project_path, cocos_creator_path, build_args)
        
        # --- 启动 Android 构建 ---
        start_android_build(build_job, project_path, safe_output_name)
        
        return f"Cocos Creator 打包成功，已启动 Android Gradle 构建任务"
        
    except BuildJob.DoesNotExist:
        logger.error(f"未找到 ID 为 {build_job_id} 的构建任务")
        return f"未找到 ID 为 {build_job_id} 的构建任务"
    
    except ValueError as ve:
        logger.error(f"Cocos 构建任务 {build_job_id} 参数错误: {ve}")
        if build_job:
            build_job.status = BuildJob.Status.FAILED
            build_job.log += f"\n错误: {ve}\n"
            build_job.save(update_fields=['status', 'log'])
        return f"Cocos 构建参数错误: {ve}"
    
    except Exception as e:
        logger.exception(f"Cocos 构建任务 {build_job_id} 发生意外错误")
        if build_job:
            build_job.status = BuildJob.Status.FAILED
            build_job.log += f"\n构建过程中发生错误: {e}\n"
            build_job.save(update_fields=['status', 'log'])
            
            # 发生错误时则释放项目锁
            if project_lock_acquired and build_job.project:
                release_project_lock(build_job.project.id)
                logger.info(f"由于错误已释放项目锁 {build_job.project.id}")
        
        # 尝试重试
        try:
            self.retry(exc=e, countdown=60)
        except self.MaxRetriesExceededError:
            logger.error(f"构建任务 {build_job_id} 超出最大重试次数")
            return f"Cocos 构建失败，超出重试次数: {e}"
        
        return f"Cocos 构建失败，错误: {e}"

def get_base_path(build_job, project):
    # --- 确定项目路径 ---
    project_path = project.local_path
    if not project_path or not os.path.isdir(project_path):
        raise ValueError(f"项目路径无效: {project_path}")

    # --- 从系统配置获取 Cocos Creator 路径 ---
    cocos_creator_path = get_system_config('cocos_creator_path')
    if not cocos_creator_path:
        raise ValueError("cocos_creator_path 系统配置项未设置，无法构建 Cocos 项目")

    build_job.log += f"使用 Cocos Creator 路径: {cocos_creator_path}\n"
    return project_path, cocos_creator_path

def get_build_args(build_job):
    app_config = build_job.app_config
    if app_config and app_config.cocos_ad_platform:
        output_name = app_config.cocos_ad_platform
    else:
        output_name = "android"
        
    build_job.log += f"使用广告平台: {output_name}\n"
        
    # 去除声音和特殊字符，防止构建时出错
    safe_output_name = ''.join(c for c in output_name if c.isalnum() or c in '_- ')

    # --- 从系统配置获取 Cocos Creator 构建名 ---
    cocos_build_name = get_system_config('cocos_build_name')
    if not cocos_build_name:
        cocos_build_name = "game"

    # --- 从系统配置获取 Cocos Creator 开始场景的UUID ---
    cocos_start_scene_uuid = get_system_config('cocos_start_scene_uuid')
    if not cocos_start_scene_uuid:
        scene_param = ""
    else: 
        scene_param = f";startScene={cocos_start_scene_uuid}"

    # 构建参数字符串，不包含多余的引号
    build_params=f";outputName={safe_output_name};name={cocos_build_name}{scene_param}"
    build_args = f"platform=android;debug=false{build_params}"
    return build_args, safe_output_name, cocos_build_name

def run_cocos_command(build_job, project_path, cocos_creator_path, build_args):
    # 首先检查任务是否已被取消
    if check_job_canceled(build_job.id):
        build_job.log += f"任务已被取消，跳过Cocos构建命令执行\n"
        build_job.save(update_fields=['log'])
        return False, "任务已被取消", -1
        
    if os.name == 'nt':
        # Windows 特殊处理
        if ' ' in cocos_creator_path and not (cocos_creator_path.startswith('"') and cocos_creator_path.endswith('"')):
            cocos_creator_path = f'"{cocos_creator_path}"'
            
        cmd = f"{cocos_creator_path} --project \"{project_path}\" --build \"{build_args}\""
        build_job.log += f"构建命令（Windows）: {cmd}\n"
        build_job.save(update_fields=['log'])
        
        # 使用支持取消的命令执行方式
        success, output, return_code = run_command_with_cancel_support(cmd, build_job.id, cwd=project_path, use_shell=True, check_interval=3)
    else:
        # Linux/Mac 处理
        build_cmd = [
            cocos_creator_path,
            "--path", project_path,
            "--build", build_args
        ]
        
        build_job.log += f"构建命令（Unix）: {' '.join(build_cmd)}\n"
        build_job.save(update_fields=['log'])
        
        # 使用支持取消的命令执行方式
        success, output, return_code = run_command_with_cancel_support(build_cmd, build_job.id, cwd=project_path, check_interval=3)

    # 32: 构建失败 - 参数不合法
    # 34: 构建失败 - 构建过程出错
    # 36: 构建成功
    build_job.log += f"命令退出码: {return_code}\n"
    build_job.log += f"Cocos Creator 输出:\n{output}\n"

    # 如果退出码是 36，则视为成功
    if return_code == 36:
        success = True
        build_job.log += "退出码 36 表示构建成功\n"

    if not success and return_code != 36:
        build_job.status = BuildJob.Status.FAILED
        build_job.log += f"Cocos 构建失败\n"
        build_job.save(update_fields=['status', 'log'])
        raise RuntimeError(f"Cocos 项目构建失败: {output}")
        
    return success, return_code

def start_android_build(build_job, project_path, safe_output_name):
    # 检查任务是否已被取消
    if check_job_canceled(build_job.id):
        build_job.log += f"任务已被取消，跳过Android构建阶段\n"
        build_job.status = BuildJob.Status.CANCELED
        build_job.save(update_fields=['log', 'status'])
        return "\u4efb\u52a1\u5df2\u88ab\u53d6\u6d88"
        
    # --- Cocos Creator 打包成功，准备启动 Gradle 构建 ---
    build_job.log += f"Cocos Creator 打包成功，正在准备 Gradle 构建...\n"
    build_job.save(update_fields=['log'])

    # 使用约定的路径：build/[output_name]
    android_project_path = os.path.join(project_path, 'build', safe_output_name)

    # 确保路径存在
    if not os.path.exists(android_project_path):
        os.makedirs(android_project_path, exist_ok=True)
        build_job.log += f"创建约定的 Android 项目路径: {android_project_path}\n"

    build_job.log += f"使用约定的 Android 项目路径: {android_project_path}\n"
    
    # --- 再次检查任务是否已被取消 ---
    if check_job_canceled(build_job.id):
        build_job.log += f"任务已被取消，跳过Android构建阶段\n"
        build_job.status = BuildJob.Status.CANCELED
        build_job.save(update_fields=['log', 'status'])
        return "\u4efb\u52a1\u5df2\u88ab\u53d6\u6d88"
    
    # --- 更新调试信息 ---
    debug_info = build_job.debug_info or ""
    debug_info += f"\n[{build_job.created.strftime('%Y-%m-%d %H:%M:%S')}] COCOS -> ANDROID 跳转\n"
    debug_info += f"Android项目路径: {android_project_path}\n"
    build_job.debug_info = debug_info
    
    # --- 更新任务状态，将当前步骤设置为 ANDROID ---
    build_job.current_step = BuildJob.BuildStep.ANDROID
    build_job.save(update_fields=['log', 'debug_info', 'current_step'])

    # 启动 Android 构建任务
    from .android import build_android_project

    # 将 Android 项目路径记录到构建任务的日志中，以便 Android 构建任务使用
    build_job.log += f"已完成 Cocos Creator 打包，启动 Android Gradle 构建...\n"
    build_job.log += f"ANDROID_PROJECT_PATH={android_project_path}\n"  # 使用日志传递路径信息
    
    # 保持状态为 BUILDING，因为整个构建过程包括 Android 阶段
    # 不设置为 SUCCESS，避免在界面上显示两次成功状态
    build_job.save(update_fields=['log'])

    # 同步调用 Android 构建任务，以便整个构建过程在一个任务中完成
    logger.info(f"同步调用 Android 构建任务，构建任务 ID: {build_job.id}")
    result = build_android_project(build_job.id)
    return result  # 返回 Android 构建的结果作为最终结果