"""
基础任务模块，提供共享的工具函数和基础设施。
"""
import subprocess
import logging
import os
import time
import threading
import signal
import platform
from pathlib import Path
from django.conf import settings
from django.utils import timezone
from ..models import SystemConfig, BuildJob

logger = logging.getLogger(__name__)

def get_system_config(key, default=None):
    """获取系统配置项。
    
    Args:
        key: 配置键名
        default: 未找到配置时返回的默认值
        
    Returns:
        找到的配置值或默认值
    """
    try:
        config = SystemConfig.objects.get(key=key)
        return config.value
    except SystemConfig.DoesNotExist:
        logger.warning(f"系统配置项 '{key}' 未找到，使用默认值: {default}")
        return default

def run_command(command, cwd=None, use_shell=False, check=True):
    """执行命令并记录输出/错误。
    
    该函数实现了命令执行的统一封装，处理了各种异常情况，
    并提供详细的日志记录。
    
    Args:
        command: 命令列表或字符串（如果 use_shell=True）
        cwd: 工作目录
        use_shell: 是否使用shell执行命令（在 Windows 上处理复杂命令时可能需要）
        check: 是否自动检查退出码（非零退出码时引发异常）
        
    Returns:
        (success, output, return_code) 元组，其中:
            success: 布尔值表示成功状态
            output: 包含命令输出或错误信息
            return_code: 命令的退出码
    """
    # 根据命令类型不同处理日志输出
    if isinstance(command, list):
        cmd_display = ' '.join(command)
    else:
        cmd_display = command
        
    logger.info(f"执行命令: {cmd_display}" + (f" 在目录 {cwd}" if cwd else ""))
    try:
        process = subprocess.run(
            command,
            check=False,  # 不自动检查退出码，我们自己处理
            capture_output=True,
            text=True,
            shell=use_shell,  # 根据需要使用 shell
            cwd=cwd,
            encoding='utf-8',
            errors='replace'  # 替换导致解码错误的字符
        )
        
        # 记录命令输出和退出码
        return_code = process.returncode
        logger.info(f"命令输出: {process.stdout.strip()}")
        logger.info(f"命令退出码: {return_code}")
        
        if process.stderr:
            logger.warning(f"命令错误: {process.stderr.strip()}")
            
        # 返回合适的结果
        if return_code == 0 or not check:
            # 成功或者不检查退出码，则返回原始输出和退出码
            success = (return_code == 0)
            return success, process.stdout + ("\n" + process.stderr if process.stderr else ""), return_code
        else:
            # 需要检查且退出码非零，返回失败状态
            stdout_snippet = process.stdout.strip()[:200] if process.stdout else "(无标准输出)"
            stderr_snippet = process.stderr.strip()[:500] if process.stderr else "(无错误输出)"
            logger.error(f"命令执行失败，退出码 {return_code}: {cmd_display}")
            output = f"标准输出: {stdout_snippet}\n错误输出: {stderr_snippet}"
            return False, output, return_code
            
    except FileNotFoundError:
        error_msg = f"错误: 命令未找到" + (f": {command[0]}" if isinstance(command, list) else "")
        logger.error(error_msg)
        return False, error_msg, 127  # 使用通用的“命令未找到”退出码
        
    except Exception as e:
        error_msg = f"执行命令时发生意外错误: {cmd_display}\n{e}"
        logger.error(error_msg)
        return False, error_msg, -1  # 使用-1表示非常错误

def check_job_canceled(build_job_id):
    """
    检查构建任务是否已被取消。
    
    Args:
        build_job_id: 构建任务ID
        
    Returns:
        True表示任务已被取消，False表示任务正常进行中
    """
    try:
        # 首先检查任务状态
        build_job = BuildJob.objects.get(pk=build_job_id)
        if build_job.status == BuildJob.Status.CANCELED:
            logger.info(f"任务 {build_job_id} 状态已取消")
            # 尝试将状态已取消的任务添加到CanceledTask表
            try:
                # 使用检查记录存在然后添加的Django方式
                # 这里不直接导入CanceledTask模型，从而允许这个函数在任何地方调用
                from django.apps import apps
                from django.utils import timezone
                
                CanceledTask = apps.get_model('build_app', 'CanceledTask')
                # 检查是否已经存在记录
                exists = CanceledTask.objects.filter(task_id=str(build_job_id)).exists()
                if not exists:
                    # 创建新记录
                    CanceledTask.objects.create(
                        task_id=str(build_job_id),
                        canceled_at=timezone.now()
                    )
                    logger.info(f"已将取消的任务 {build_job_id} 添加到CanceledTask表")
            except Exception as e:
                logger.error(f"尝试将已取消任务加入CanceledTask表时出错: {e}")
            
            return True
            
        # 如果任务状态不是取消，检查CanceledTask表
        try:
            # 使用Django ORM查询CanceledTask表
            from django.apps import apps
            CanceledTask = apps.get_model('build_app', 'CanceledTask')
            
            # 检查任务是否在CanceledTask表中
            exists = CanceledTask.objects.filter(task_id=str(build_job_id)).exists()
            if exists:
                logger.info(f"任务 {build_job_id} 在CanceledTask表中发现记录")
                # 更新任务状态为已取消，确保一致性
                if build_job.status != BuildJob.Status.CANCELED:
                    build_job.status = BuildJob.Status.CANCELED
                    build_job.save(update_fields=['status'])
                    logger.info(f"根据CanceledTask表记录更新任务 {build_job_id} 状态为已取消")
                return True
        except Exception as db_error:
            logger.error(f"检查CanceledTask表时出错: {db_error}")
            
        return False
    except Exception as e:
        logger.error(f"检查任务 {build_job_id} 取消状态时出错: {e}")
        return False

def run_command_with_cancel_support(command, build_job_id, cwd=None, use_shell=False, check_interval=3):
    """
    执行命令，并支持随时取消。
    该函数会在后台线程中定期检查任务状态，如果发现任务被取消，则终止命令执行。
    
    Args:
        command: 命令列表或字符串
        build_job_id: 构建任务ID，用于检查任务是否被取消
        cwd: 工作目录
        use_shell: 是否使用shell执行命令
        check_interval: 检查任务状态的间隔（秒）
        
    Returns:
        (success, output, return_code) 元组
    """
    if isinstance(command, list):
        cmd_display = ' '.join(command)
    else:
        cmd_display = command
        
    logger.info(f"执行命令（支持取消）: {cmd_display}" + (f" 在目录 {cwd}" if cwd else ""))
    
    # 检查任务是否已被取消
    if check_job_canceled(build_job_id):
        logger.info(f"任务 {build_job_id} 已被取消，跳过命令执行")
        return False, "任务已被取消，跳过命令执行", -1
    
    # 创建进程
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        shell=use_shell,
        cwd=cwd,
        text=True,
        encoding='utf-8',
        errors='replace'
    )
    
    stdout_data = []
    stderr_data = []
    process_terminated = False
    
    # 创建读取输出的线程
    def read_output():
        for line in iter(process.stdout.readline, ""):
            stdout_data.append(line)
        process.stdout.close()
            
    def read_error():
        for line in iter(process.stderr.readline, ""):
            stderr_data.append(line)
        process.stderr.close()
    
    stdout_thread = threading.Thread(target=read_output, daemon=True)
    stderr_thread = threading.Thread(target=read_error, daemon=True)
    stdout_thread.start()
    stderr_thread.start()
    
    # 检查任务是否被取消的线程
    def check_cancel():
        nonlocal process_terminated
        while process.poll() is None and not process_terminated:
            if check_job_canceled(build_job_id):
                logger.warning(f"任务 {build_job_id} 被取消，终止命令执行: {cmd_display}")
                # 在不同平台上终止进程
                try:
                    if platform.system() == "Windows":
                        # Windows下使用taskkill强制终止进程树
                        subprocess.run(["taskkill", "/F", "/T", "/PID", str(process.pid)])
                    else:
                        # Unix系统使用SIGTERM信号
                        os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                    process_terminated = True
                except Exception as e:
                    logger.error(f"终止进程时出错: {e}")
            time.sleep(check_interval)
    
    cancel_thread = threading.Thread(target=check_cancel, daemon=True)
    cancel_thread.start()
    
    # 等待进程完成
    process.wait()
    
    # 确保线程终止
    stdout_thread.join(timeout=1)
    stderr_thread.join(timeout=1)
    
    # 收集输出
    stdout_text = "".join(stdout_data)
    stderr_text = "".join(stderr_data)
    return_code = process.returncode
    
    # 判断是否成功
    success = (return_code == 0 and not process_terminated)
    output = stdout_text + ("\n" + stderr_text if stderr_text else "")
    
    if process_terminated:
        logger.info(f"命令执行被取消。退出码: {return_code}")
        return False, "命令执行被取消", -1
    
    if success:
        logger.info(f"命令执行成功。退出码: {return_code}")
    else:
        logger.warning(f"命令执行失败。退出码: {return_code}")
        logger.warning(f"错误输出: {stderr_text[:500] if len(stderr_text) > 500 else stderr_text}")
    
    return success, output, return_code

def ensure_directory(path):
    """确保目录存在，如果不存在则创建。
    
    Args:
        path: 要确保存在的目录路径
        
    Returns:
        创建的目录路径
    """
    directory = Path(path)
    directory.mkdir(parents=True, exist_ok=True)
    return directory
