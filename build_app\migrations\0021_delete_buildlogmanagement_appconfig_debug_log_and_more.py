# Generated by Django 4.2.20 on 2025-05-19 07:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('build_app', '0020_alter_buildjob_current_step_and_more'),
    ]

    operations = [
        migrations.DeleteModel(
            name='BuildLogManagement',
        ),
        migrations.AddField(
            model_name='appconfig',
            name='debug_log',
            field=models.BooleanField(default=False, help_text='是否启用调试日志模式', verbose_name='调试模式'),
        ),
        migrations.AddField(
            model_name='appconfig',
            name='invite_code',
            field=models.CharField(blank=True, help_text='应用使用的代理码', max_length=50, verbose_name='代理码'),
        ),
    ]
