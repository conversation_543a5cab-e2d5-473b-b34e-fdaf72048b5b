# Generated by Django 4.2.20 on 2025-05-24 03:11

import build_app.models.appConfig
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('build_app', '0024_appconfig_weixin_app_id_appconfig_weixin_app_secret'),
    ]

    operations = [
        migrations.AddField(
            model_name='appconfig',
            name='keystore_file',
            field=models.FileField(blank=True, help_text='上传已存在的JKS文件。如果提供，将优先使用该文件而非自动生成。', null=True, upload_to=build_app.models.appConfig.app_config_jks_upload_path, verbose_name='密钥库文件 (JKS)'),
        ),
    ]
