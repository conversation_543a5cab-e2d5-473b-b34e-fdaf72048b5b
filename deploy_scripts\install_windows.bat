@echo off
chcp 65001 > nul
REM ======================================================
REM DjangoAutoBuild Windows部署脚本
REM 此脚本用于在Windows环境下安装和配置DjangoAutoBuild项目
REM ======================================================

echo ===== DjangoAutoBuild Windows部署脚本 =====
echo.

REM 设置项目路径变量
set PROJECT_DIR=%~dp0..
cd %PROJECT_DIR%
echo 项目目录: %PROJECT_DIR%
echo.

REM 检查Python是否已安装
python --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 未检测到Python。请安装Python 3.10+并确保添加到PATH。
    echo 下载地址: https://www.python.org/downloads/windows/
    exit /b 1
)

echo [√] Python已安装
python --version
echo.

REM 创建虚拟环境
if not exist .venv (
    echo 正在创建虚拟环境...
    python -m venv .venv
) else (
    echo [√] 虚拟环境已存在
)

REM 激活虚拟环境
call .venv\Scripts\activate
echo [√] 虚拟环境已激活
echo.

REM 安装依赖
echo 正在安装/更新依赖...
pip install -U pip
pip install uv waitress gevent

REM 使用uv安装项目依赖
if exist pyproject.toml (
    echo 使用uv安装项目依赖...
    uv sync
) else (
    echo [警告] 未找到pyproject.toml，使用requirements.txt...
    if exist requirements.txt (
        pip install -r requirements.txt
    ) else (
        echo [错误] 未找到依赖文件。请确保pyproject.toml或requirements.txt存在。
        exit /b 1
    )
)

echo [√] 依赖安装完成
echo.

REM 检查.env文件
if not exist .env (
    if exist .env.example (
        echo 正在从.env.example创建.env文件...
        copy .env.example .env
        echo [!] 请编辑.env文件，设置正确的配置信息
    ) else (
        echo [警告] 未找到.env.example文件，创建空的.env文件...
        echo # DjangoAutoBuild环境变量配置 > .env
        echo DJANGO_SECRET_KEY=请替换为安全的密钥 >> .env
        echo DJANGO_DEBUG=False >> .env
        echo DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1 >> .env
        echo [!] 请编辑.env文件，设置正确的配置信息
    )
) else (
    echo [√] .env文件已存在
)

echo.
echo ===== 数据库配置 =====

REM 询问数据库类型
echo 请选择数据库类型:
echo 1. SQLite (简单，适合开发)
echo 2. PostgreSQL (推荐用于生产)
set /p DB_CHOICE="请输入选择 (1/2): "

if "%DB_CHOICE%"=="2" (
    echo.
    echo 请确保已安装PostgreSQL并创建了数据库
    echo 请在.env文件中设置以下数据库连接信息:
    echo DATABASE_URL=postgres://用户名:密码@localhost:5432/数据库名
    echo 或设置单独的数据库参数:
    echo DB_NAME=数据库名
    echo DB_USER=用户名
    echo DB_PASSWORD=密码
    echo DB_HOST=localhost
    echo DB_PORT=5432
)

echo.
echo ===== 初始化数据库 =====
echo 正在执行数据库迁移...

REM 确保使用激活的虚拟环境执行Django命令
call .venv\Scripts\activate
python manage.py migrate
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 数据库迁移失败。请检查数据库配置。
    echo 尝试使用uv运行命令...
    uv pip install django
    uv run python manage.py migrate
    if %ERRORLEVEL% NEQ 0 (
        echo [错误] 使用uv运行迁移也失败了。请检查数据库配置和uv安装。
        exit /b 1
    )
)

echo [√] 数据库迁移成功
echo.

REM 询问是否创建超级用户
set /p CREATE_SUPERUSER="是否创建管理员账户? (y/n): "
if /i "%CREATE_SUPERUSER%"=="y" (
    echo 创建管理员账户...
    call .venv\Scripts\activate
    python manage.py createsuperuser
    if %ERRORLEVEL% NEQ 0 (
        echo 尝试使用uv运行命令...
        uv run python manage.py createsuperuser
    )
)

REM 收集静态文件
echo.
echo ===== 收集静态文件 =====
echo 正在收集静态文件...
call .venv\Scripts\activate
python manage.py collectstatic --noinput
if %ERRORLEVEL% NEQ 0 (
    echo 尝试使用uv运行命令...
    uv run python manage.py collectstatic --noinput
)
echo [√] 静态文件收集完成
echo.

echo ===== 部署脚本创建 =====
echo 正在创建服务启动脚本...

REM 创建Web服务启动脚本
echo @echo off > run_server.bat
echo REM DjangoAutoBuild Web服务启动脚本 >> run_server.bat
echo. >> run_server.bat
echo REM 检查虚拟环境是否存在 >> run_server.bat
echo if not exist ".venv\Scripts\activate.bat" ( >> run_server.bat
echo     echo 错误: 虚拟环境不存在，请先创建虚拟环境 >> run_server.bat
echo     exit /b 1 >> run_server.bat
echo ) >> run_server.bat
echo. >> run_server.bat
echo REM 激活虚拟环境 >> run_server.bat
echo echo 正在激活虚拟环境... >> run_server.bat
echo call ".venv\Scripts\activate.bat" >> run_server.bat
echo. >> run_server.bat
echo REM 检查waitress是否已安装 >> run_server.bat
echo pip show waitress ^> nul 2^>^&1 >> run_server.bat
echo if %%ERRORLEVEL%% NEQ 0 ( >> run_server.bat
echo     echo 正在安装waitress... >> run_server.bat
echo     pip install waitress >> run_server.bat
echo     if %%ERRORLEVEL%% NEQ 0 ( >> run_server.bat
echo         echo 错误: 安装waitress失败 >> run_server.bat
echo         exit /b 1 >> run_server.bat
echo     ) >> run_server.bat
echo ) >> run_server.bat
echo. >> run_server.bat
echo REM 启动服务器 >> run_server.bat
echo echo 启动Web服务器... >> run_server.bat
echo python -m waitress --port=8000 autobuild_project.wsgi:application >> run_server.bat
echo [√] Web服务启动脚本已创建: run_server.bat

REM 创建Celery服务启动脚本
echo @echo off > run_celery.bat
echo REM DjangoAutoBuild Celery Worker启动脚本 >> run_celery.bat
echo. >> run_celery.bat
echo REM 检查虚拟环境是否存在 >> run_celery.bat
echo if not exist ".venv\Scripts\activate.bat" ( >> run_celery.bat
echo     echo 错误: 虚拟环境不存在，请先创建虚拟环境 >> run_celery.bat
echo     exit /b 1 >> run_celery.bat
echo ) >> run_celery.bat
echo. >> run_celery.bat
echo REM 激活虚拟环境 >> run_celery.bat
echo echo 正在激活虚拟环境... >> run_celery.bat
echo call ".venv\Scripts\activate.bat" >> run_celery.bat
echo. >> run_celery.bat
echo REM 检查celery是否已安装 >> run_celery.bat
echo pip show celery ^> nul 2^>^&1 >> run_celery.bat
echo if %%ERRORLEVEL%% NEQ 0 ( >> run_celery.bat
echo     echo 正在安装celery和gevent... >> run_celery.bat
echo     pip install celery gevent >> run_celery.bat
echo     if %%ERRORLEVEL%% NEQ 0 ( >> run_celery.bat
echo         echo 错误: 安装celery和gevent失败 >> run_celery.bat
echo         exit /b 1 >> run_celery.bat
echo     ) >> run_celery.bat
echo ) >> run_celery.bat
echo. >> run_celery.bat
echo REM 设置环境变量 >> run_celery.bat
echo set FORKED_BY_MULTIPROCESSING=1 >> run_celery.bat
echo. >> run_celery.bat
echo REM 启动Celery Worker >> run_celery.bat
echo echo 启动Celery Worker... >> run_celery.bat
echo celery -A autobuild_project worker --pool=gevent --concurrency=2 --loglevel=info >> run_celery.bat
echo [√] Celery Worker启动脚本已创建: run_celery.bat

REM 创建备份脚本
mkdir backup 2>nul
echo @echo off > backup\backup_db.bat
echo REM DjangoAutoBuild 数据库和媒体文件备份脚本 >> backup\backup_db.bat
echo set BACKUP_DIR=%PROJECT_DIR%\backup\%%date:~0,4%%-%%date:~5,2%%-%%date:~8,2%% >> backup\backup_db.bat
echo mkdir %%BACKUP_DIR%% 2^>nul >> backup\backup_db.bat
echo echo 正在备份数据库和媒体文件到 %%BACKUP_DIR%% >> backup\backup_db.bat
echo. >> backup\backup_db.bat
echo REM 备份SQLite数据库 >> backup\backup_db.bat
echo if exist "%PROJECT_DIR%\db.sqlite3" ( >> backup\backup_db.bat
echo   copy "%PROJECT_DIR%\db.sqlite3" "%%BACKUP_DIR%%\db.sqlite3.bak" >> backup\backup_db.bat
echo   echo [√] SQLite数据库已备份 >> backup\backup_db.bat
echo ) >> backup\backup_db.bat
echo. >> backup\backup_db.bat
echo REM 备份PostgreSQL数据库 - 如果使用PostgreSQL，请取消下面的注释并配置 >> backup\backup_db.bat
echo REM set PGPASSWORD=your_password >> backup\backup_db.bat
echo REM pg_dump -U your_username -F c your_database ^> "%%BACKUP_DIR%%\db_backup.dump" >> backup\backup_db.bat
echo. >> backup\backup_db.bat
echo REM 备份媒体文件 >> backup\backup_db.bat
echo xcopy /E /I /Y "%PROJECT_DIR%\media" "%%BACKUP_DIR%%\media" >> backup\backup_db.bat
echo echo [√] 媒体文件已备份 >> backup\backup_db.bat
echo. >> backup\backup_db.bat
echo echo 备份完成! >> backup\backup_db.bat
echo [√] 备份脚本已创建: backup\backup_db.bat

echo.
echo ===== 安装服务 =====
echo 要将应用安装为Windows服务，您需要下载NSSM工具:
echo 下载地址: https://nssm.cc/download
echo.
echo 下载后，使用以下命令安装服务:
echo nssm install DjangoAutoBuild_Web "%PROJECT_DIR%\run_server.bat"
echo nssm install DjangoAutoBuild_Celery "%PROJECT_DIR%\run_celery.bat"
echo.

echo ===== 部署完成 =====
echo.
echo DjangoAutoBuild项目部署已完成!
echo.
echo 后续步骤:
echo 1. 编辑.env文件，确保配置正确
echo 2. 在Django管理后台更新SystemConfig中的路径配置
echo 3. 启动服务:
echo    - 手动启动: 运行run_server.bat和run_celery.bat
echo    - 作为服务: 使用NSSM安装并启动服务
echo.
echo 访问地址: http://localhost:8000/admin
echo.
echo 感谢使用DjangoAutoBuild!
