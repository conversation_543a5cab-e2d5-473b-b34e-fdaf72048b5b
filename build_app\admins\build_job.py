"""
构建任务管理类
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.safestring import mark_safe
from django.urls import path, reverse
from django.http import HttpResponseRedirect
from django.contrib import messages
from django.utils import timezone
import os
import logging

from ..views import trigger_build_view, build_log_view
from ..models import BuildJob, CanceledTask


logger = logging.getLogger(__name__)


class BuildJobAdmin(admin.ModelAdmin):
    """构建任务管理类"""
    list_display = ('id', 'project', 'app_config', 'branch_or_tag', 'status_badge', 'progress_bar',
        'output_file_link', 'view_log_button', 'cancel_button', 'triggered_by', 'modified')
    list_filter = ('status', 'project', 'app_config__company', 'created')
    search_fields = ('id', 'project__name', 'app_config__config_name', 'branch_or_tag', 'commit_id')
    readonly_fields = (
        'project', 'app_config', 'branch_or_tag', 'commit_id', 'status',
        'start_time', 'end_time', 'log_message', 'output_file_path',
        'triggered_by', 'created', 'modified'
    )
    list_display_links = ('id', 'project')

    fieldsets = (
        (None, {
            'fields': ('project', 'app_config', 'status')
        }),
        ('代码信息', {
            'fields': ('branch_or_tag', 'commit_id')
        }),
        ('执行信息', {
            'fields': ('start_time', 'end_time', 'log_message', 'output_file_path')
        }),
        ('元数据', {
            'fields': ('triggered_by', 'created', 'modified')
        }),
    )

    change_list_template = "admin/build_app/buildjob/change_list.html"

    def has_add_permission(self, request):
        """禁止手动添加构建任务"""
        return False

    def has_change_permission(self, request, obj=None):
        """禁止编辑构建任务"""
        return False

    def view_log_button(self, obj):
        """添加查看日志按钮"""
        url = reverse('build_app:build_log_detail', args=[obj.id])
        return mark_safe(f'<a href="{url}" class="button" style="background-color: #417690; color: white; padding: 5px 10px; text-decoration: none; border-radius: 4px;">查看日志</a>')
    view_log_button.short_description = '日志'

    def get_urls(self):
        """添加自定义URL"""
        urls = super().get_urls()
        custom_urls = [
            path('trigger/', self.admin_site.admin_view(trigger_build_view), name='build_app_buildjob_trigger'),
            path('cancel/<int:build_job_id>/', self.admin_site.admin_view(self.cancel_build_view), name='build_app_buildjob_cancel'),
        ]
        return custom_urls + urls

    def cancel_button(self, obj):
        """添加取消按钮，只对正在运行的任务显示"""
        # 只对以下状态显示取消按钮
        running_states = [BuildJob.Status.PENDING, BuildJob.Status.PREPARING,
                          BuildJob.Status.BUILDING, BuildJob.Status.PACKAGING]

        if obj.status in running_states:
            url = reverse('admin:build_app_buildjob_cancel', args=[obj.id])
            return mark_safe(f'<a href="{url}" class="button" style="background-color: #e74c3c; color: white; padding: 5px 10px; text-decoration: none; border-radius: 4px;">终止</a>')
        return '-'
    cancel_button.short_description = '终止任务'

    def _check_can_cancel(self, build_job):
        """检查任务是否可以取消"""
        # 如果任务已经是取消状态，则只显示信息而不重复处理
        if build_job.status == BuildJob.Status.CANCELED:
            return False, f'任务 #{build_job.id} 已经处于取消状态。'

        # 检查当前状态是否可取消
        running_states = [BuildJob.Status.PENDING, BuildJob.Status.PREPARING,
                          BuildJob.Status.BUILDING, BuildJob.Status.PACKAGING]

        if build_job.status not in running_states:
            return False, f'任务 #{build_job.id} 不在运行状态，无法终止。'
        
        return True, None

    def _update_build_job_status(self, build_job):
        """更新构建任务状态为已取消"""
        prev_status = build_job.get_status_display()
        build_job.status = BuildJob.Status.CANCELED
        build_job.log += f"\n\n[{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}] 用户手动终止任务，原状态: {prev_status}\n"
        build_job.save(update_fields=['status', 'log'])

    def _cancel_celery_tasks(self, build_job_id):
        """取消Celery执行中的任务"""
        try:
            # 使用 Celery app 直接取消任务
            from autobuild_project.celery import app
            # 获取所有活动任务
            inspect = app.control.inspect()
            active_tasks = inspect.active() or {}
            reserved_tasks = inspect.reserved() or {}
            scheduled_tasks = inspect.scheduled() or {}

            # 尝试多种可能的任务名称模式
            task_id_patterns = [
                str(build_job_id),
                f"build_job:{build_job_id}",
                f"process_build_job-{build_job_id}",
                f"build_cocos_project-{build_job_id}",
                f"build_android_project-{build_job_id}"
            ]

            # 查找并撤销所有相关任务
            found_tasks = []

            # 检查所有活动、预留和计划任务
            for tasks_dict in [active_tasks, reserved_tasks, scheduled_tasks]:
                for worker, tasks in tasks_dict.items():
                    for task in tasks:
                        task_id = task.get('id', '')
                        task_name = task.get('name', '')
                        args = str(task.get('args', ''))

                        # 检查是否与目标任务相关
                        if any(pattern in task_id for pattern in task_id_patterns) or \
                           any(pattern in args for pattern in task_id_patterns):
                            found_tasks.append(task_id)

            # 取消找到的所有相关任务
            for task_id in found_tasks:
                app.control.revoke(task_id, terminate=True, signal='SIGTERM')

            # 如果没有找到特定任务，尝试使用一般模式取消
            if not found_tasks:
                common_task_names = [
                    f"process_build_job-{build_job_id}",
                    f"build_cocos_project-{build_job_id}",
                    f"build_android_project-{build_job_id}",
                    f"celery-task-meta-{build_job_id}"
                ]
                for task_name in common_task_names:
                    try:
                        app.control.revoke(task_name, terminate=True, signal='SIGTERM')
                    except Exception:
                        pass

            # 在日志中记录取消的任务数量
            logger.info(f"为构建任务 {build_job_id} 取消了 {len(found_tasks)} 个相关Celery任务")
        except Exception as e:
            logger.error(f"取消Celery任务时出错: {e}")

    def _cleanup_redis_resources(self, build_job):
        """清理Redis中的任务相关资源"""
        try:
            from django_redis import get_redis_connection
            redis_client = get_redis_connection("default")

            # 清除所有可能存在的任务键
            patterns = [
                f"*:build_job:{build_job.id}*",
                f"*:{build_job.id}*",
                f"*:celery-task-meta-*",
                f"unacked_index*"
            ]
            for pattern in patterns:
                task_keys = redis_client.keys(pattern)
                if task_keys:
                    redis_client.delete(*task_keys)

            # 在项目有锁的情况下释放锁
            if build_job.project:
                from ..tasks.lock import release_project_lock
                release_project_lock(build_job.project.id)
        except Exception as e:
            # 记录错误但不中断操作
            logger.error(f"清理任务 {build_job.id} 的Redis资源时出错: {e}")

    def _add_to_canceled_tasks(self, build_job_id):
        """将任务添加到CanceledTask表中"""
        try:
            # 使用get_or_create避免唯一约束错误
            CanceledTask.objects.get_or_create(
                task_id=str(build_job_id),
                defaults={'canceled_at': timezone.now()}
            )
        except Exception as e:
            logger.error(f"准备CanceledTask记录时出错: {e}")

    def cancel_build_view(self, request, build_job_id):
        """
        取消构建任务的视图函数。
        将任务状态设置为CANCELED，并尝试清理相关资源。
        """
        try:
            # 获取构建任务
            build_job = BuildJob.objects.get(pk=build_job_id)

            # 检查任务是否可以取消
            can_cancel, error_msg = self._check_can_cancel(build_job)
            if not can_cancel:
                messages.info(request, error_msg)
                return HttpResponseRedirect(reverse('admin:build_app_buildjob_changelist'))

            # 更新任务状态为已取消
            self._update_build_job_status(build_job)
            
            # 取消Celery执行中的任务
            self._cancel_celery_tasks(build_job_id)
            
            # 清理Redis中的任务相关资源
            self._cleanup_redis_resources(build_job)
            
            # 将任务添加到CanceledTask表中
            self._add_to_canceled_tasks(build_job_id)

            # 显示成功消息
            messages.success(request, f'成功终止任务 #{build_job_id}。刷新页面不会重新启动任务。')

        except BuildJob.DoesNotExist:
            messages.error(request, f'未找到构建任务: #{build_job_id}')
        except Exception as e:
            messages.error(request, f'终止任务时出错: {str(e)}')

        # 重定向回列表页
        return HttpResponseRedirect(reverse('admin:build_app_buildjob_changelist'))

    def status_badge(self, obj):
        """为状态增加颜色徽章显示"""
        status_colors = {
            BuildJob.Status.PENDING: '#f9ca24',   # 黄色
            BuildJob.Status.PREPARING: '#f0932b', # 橙色
            BuildJob.Status.BUILDING: '#6ab04c',  # 浅绿色
            BuildJob.Status.PACKAGING: '#4834d4', # 蓝色
            BuildJob.Status.UPLOADING: '#22a6b3', # 青色
            BuildJob.Status.SUCCESS: '#27ae60',   # 绿色
            BuildJob.Status.FAILED: '#eb4d4b',    # 红色
            BuildJob.Status.CANCELED: '#95a5a6',  # 灰色
        }

        status_name = obj.get_status_display()
        color = status_colors.get(obj.status, '#95a5a6')  # 默认灰色

        return mark_safe(f'<span class="status-badge" style="background-color: {color}">{status_name}</span>')
    status_badge.short_description = '状态'
    status_badge.admin_order_field = 'status'

    def progress_bar(self, obj):
        """显示进度条指示器"""
        # 定义每种状态的完成比例
        progress_map = {
            BuildJob.Status.PENDING: 10,     # 10%
            BuildJob.Status.PREPARING: 25,   # 25%
            BuildJob.Status.BUILDING: 50,    # 50%
            BuildJob.Status.PACKAGING: 75,   # 75%
            BuildJob.Status.UPLOADING: 90,   # 90%
            BuildJob.Status.SUCCESS: 100,    # 100%
            BuildJob.Status.FAILED: 100,     # 100%
            BuildJob.Status.CANCELED: 100,   # 100%
        }

        progress = progress_map.get(obj.status, 0)

        # 根据状态决定进度条颜色
        if obj.status == BuildJob.Status.SUCCESS:
            bar_class = 'progress-success'
        elif obj.status == BuildJob.Status.FAILED:
            bar_class = 'progress-failed'
        elif obj.status == BuildJob.Status.CANCELED:
            bar_class = 'progress-canceled'
        else:
            bar_class = 'progress-running'

        html = f'''
        <div class="progress-container">
            <div class="progress-bar {bar_class}" style="width: {progress}%"></div>
            <div class="progress-text">{progress}%</div>
        </div>
        '''

        return mark_safe(html)
    progress_bar.short_description = '进度'

    def output_file_link(self, obj):
        """显示指向产物目录的文件夹图标链接"""
        if not obj.output_file_path:
            return '-'

        # 获取app_config的ID用于构建文件管理器URL
        app_config_id = obj.app_config.id

        # 构建指向文件管理器的URL
        file_manager_url = reverse('admin:file_manager_app', args=[app_config_id])

        # 获取文件名
        filename = os.path.basename(obj.output_file_path)

        # 返回文件夹图标链接
        return mark_safe(f'<a href="{file_manager_url}" target="_blank" title="点击浏览产物目录">📁 {filename}</a>')
    output_file_link.short_description = '产物路径'
    output_file_link.admin_order_field = 'output_file_path'
