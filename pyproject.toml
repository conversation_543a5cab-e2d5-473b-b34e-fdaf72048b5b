[project]
name = "django_auto_build"
version = "0.1.0"
description = "Web application for managing Cocos project builds and packaging using Django."
authors = [
    {name = "Your Name", email = "<EMAIL>"}, # TODO: Update author details
]
requires-python = ">=3.10"

# Add your dependencies here
dependencies = [
    "django>=4.2,<5.21", # Or your preferred Django LTS/version
    "celery[redis]>=5.3,<6.0",
    "psycopg2-binary>=2.9,<3.0", # For PostgreSQL
    "python-dotenv>=1.0,<2.0", # For environment variables
    "gunicorn>=21.0,<22.0", # WSGI server
    "dj-database-url>=2.0,<3.0", # For parsing DATABASE_URL
    "django-jazzmin>=2.6,<3.0", # Modern admin theme
    "gevent>=23.0,<24.0", # Add gevent for Celery worker on Windows
    "django-model-utils>=4.3,<5.0", # 添加 django-model-utils
    "flower>=2.0.1,<3.0", # Celery监控工具
    # Add other dependencies like requests, djangorestframework if needed
    "vine>=5.1.0",
    "pillow>=11.2.1",
    "flower>=2.0.1",
    "pypinyin>=0.50.0", # 中文转拼音库
    "paramiko>=3.3.1", # SSH/SFTP客户端库
    "waitress>=3.0.2",
]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.uv]
# Optional: uv specific configurations if needed

[tool.setuptools.packages.find]
# Explicitly include only our Django project and app directories
where = ["."]  # Root directory
include = ["autobuild_project*", "build_app*"]
exclude = ["tests*", ".*", "*tests*"] # Exclude tests, hidden files, etc.
