from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import Company, Project, AppConfig, BuildJob
from django.http import JsonResponse, HttpResponse
from .utils import generate_config_files
from .tasks.config import process_build_job
import os
import json
import datetime

# Create your views here.

@login_required # Ensure only logged-in users can access
def trigger_build_view(request):
    if request.method == 'POST':
        company_id = request.POST.get('company_id')
        project_id = request.POST.get('project_id')
        app_config_ids_str = request.POST.get('app_config_ids') # Comma-separated string
        branch_or_tag = request.POST.get('branch_or_tag', '').strip() # Optional

        # Basic Validation
        if not project_id or not app_config_ids_str:
            messages.error(request, "必须选择一个项目和至少一个应用配置。")
            # Re-render form with submitted data for correction
            companies = Company.objects.filter(is_active=True).order_by('name')
            # Pass POST data back to context to repopulate form
            context = {
                'companies': companies,
                'request': request # Pass request to access POST data in template
            }
            return render(request, 'build_app/trigger_build.html', context)

        try:
            project = Project.objects.get(pk=project_id)
            app_config_ids = [int(id) for id in app_config_ids_str.split(',') if id.isdigit()]

            if not app_config_ids:
                messages.error(request, "未选择有效的应用配置。")
                # Re-render form with submitted data for correction
                companies = Company.objects.filter(is_active=True).order_by('name')
                context = {'companies': companies, 'request': request}
                return render(request, 'build_app/trigger_build.html', context)

            created_jobs_count = 0
            for config_id in app_config_ids:
                try:
                    app_config = AppConfig.objects.get(pk=config_id, company_id=company_id, is_active=True) # Ensure config belongs to company and is active

                    # 检查是否需要自动上传
                    auto_upload = request.POST.get('auto_upload') == '1'
                    print(f"AUTO_UPLOAD checkbox value: {request.POST.get('auto_upload')}, auto_upload={auto_upload}")
                    
                    # Create BuildJob with auto_upload field
                    build_job = BuildJob.objects.create(
                        project=project,
                        app_config=app_config,
                        triggered_by=request.user, # Associate with the logged-in user
                        status=BuildJob.Status.PENDING, # Corrected status enum reference
                        branch_or_tag=branch_or_tag if branch_or_tag else project.default_branch, # Use default if empty
                        auto_upload=auto_upload  # 设置自动上传字段
                        # log field will be populated by the build process
                    )
                    created_jobs_count += 1
                    
                    print(f"创建了构建任务 ID: {build_job.id}, 自动上传设置为: {auto_upload}")

                    # Trigger the Celery task asynchronously
                    process_build_job.delay(build_job.id)
                    print(f"Dispatched Celery task process_build_job for BuildJob ID: {build_job.id}, auto_upload={auto_upload}")

                except AppConfig.DoesNotExist:
                    messages.warning(request, f"无法找到或无权访问配置 ID {config_id}，已跳过。")
                except Exception as e:
                    messages.error(request, f"为配置 ID {config_id} 创建构建任务时出错: {e}")

            if created_jobs_count > 0:
                messages.success(request, f"已成功创建 {created_jobs_count} 个构建任务。")
            else:
                messages.warning(request, "未能创建任何构建任务。请检查选择的配置是否有效。")

            # 重定向到Admin中的构建任务列表页面
            return redirect('admin:build_app_buildjob_changelist')

        except Project.DoesNotExist:
            messages.error(request, "选择的项目无效。")
        except Exception as e:
            messages.error(request, f"处理构建请求时发生意外错误: {e}")
            # Log the full error for debugging
            print(f"Error processing build request: {e}")


    # Handle GET request or failed POST validation: Render the initial form
    companies = Company.objects.filter(is_active=True).order_by('name')
    context = {
        'companies': companies,
        'request': request # Pass request for potential POST data repopulation in template
    }
    return render(request, 'build_app/trigger_build.html', context)

# --- 构建日志查看页面 ---
@login_required
def build_log_view(request, build_job_id):
    """构建日志查看页面，展示详细的构建日志和调试信息。"""
    # 获取构建任务对象
    build_job = get_object_or_404(BuildJob, pk=build_job_id)
    
    # 准备上下文数据
    context = {
        'build_job': build_job,
        'build_steps': BuildJob.BuildStep.choices,
        'page_title': f'构建日志 - 任务 #{build_job_id}'
    }
    
    return render(request, 'build_app/build_log.html', context)


@login_required
def build_log_list_view(request):
    """构建日志列表页面，现在改为重定向到构建任务管理页面。"""
    # 直接重定向到构建任务列表
    return redirect('admin:build_app_buildjob_changelist')


# --- AJAX 日志更新接口 ---
def ajax_get_build_log(request, build_job_id):
    """AJAX接口，用于获取构建日志的最新内容。"""
    if request.method == 'GET':
        try:
            build_job = BuildJob.objects.get(pk=build_job_id)
            
            # 准备返回的数据
            data = {
                'id': build_job.id,
                'status': build_job.status,
                'current_step': build_job.current_step,
                'log': build_job.log,
                'debug_info': build_job.debug_info,
                'created': build_job.created.strftime('%Y-%m-%d %H:%M:%S'),
                'updated': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'is_completed': build_job.status in [BuildJob.Status.SUCCESS, BuildJob.Status.FAILED]
            }
            
            return JsonResponse(data)
        except BuildJob.DoesNotExist:
            return JsonResponse({'error': f'构建任务 #{build_job_id} 不存在'}, status=404)
    
    return JsonResponse({'error': '请求方法不支持'}, status=400)


# --- AJAX Views ---
@login_required
def ajax_get_projects(request):
    # This view now returns ALL projects with SUCCESS status, regardless of company.
    projects_data = []

    try:
        # Fetch all successfully synced projects
        projects = Project.objects.filter(sync_status=Project.SyncStatus.SUCCESS).order_by('name')

        for project in projects:
            projects_data.append({
                'id': project.id,
                'name': project.name
            })
        return JsonResponse({'projects': projects_data})
    except ValueError:
        return JsonResponse({'error': 'Invalid company ID'}, status=400)
    except Exception as e:
        # Log the exception e
        return JsonResponse({'error': 'Could not retrieve projects'}, status=500)

@login_required
def ajax_get_configs(request):
    company_id = request.GET.get('company_id')
    configs_data = []
    if company_id:
        try:
            # Find all active AppConfigs for the company
            company_configs = AppConfig.objects.filter(company_id=company_id, is_active=True)
            for config in company_configs:
                configs_data.append({
                    'id': config.id,
                    'name': config.config_name # Use the correct field name
                })
            return JsonResponse({'configs': configs_data})
        except Exception as e:
            return JsonResponse({'error': str(e)})
    return JsonResponse({'error': 'Invalid company ID'})
