"""
上传文件记录模型
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from model_utils.models import TimeStampedModel

class UploadedFile(TimeStampedModel):
    """
    记录成功上传到远程服务器的文件信息
    """
    company_name = models.CharField(
        _("公司名称"),
        max_length=100,
        help_text=_("文件所属公司名称")
    )
    project_name = models.CharField(
        _("项目名称"),
        max_length=100,
        help_text=_("文件所属项目名称")
    )
    config_name = models.CharField(
        _("配置名称"),
        max_length=100,
        help_text=_("文件所属应用配置名称")
    )
    app_name = models.CharField(
        _("应用显示名称"),
        max_length=100,
        help_text=_("应用的显示名称")
    )
    file_name = models.CharField(
        _("文件名"),
        max_length=255,
        help_text=_("上传的文件名")
    )
    file_size = models.IntegerField(
        _("文件大小(字节)"),
        default=0,
        help_text=_("上传文件的大小(字节)")
    )
    download_link = models.URLField(
        _("下载链接"),
        max_length=255,
        help_text=_("文件的下载链接")
    )
    remote_path = models.CharField(
        _("远程路径"),
        max_length=255,
        help_text=_("文件在远程服务器上的路径")
    )
    remote_server = models.CharField(
        _("远程服务器"),
        max_length=255,
        help_text=_("上传到的远程服务器地址")
    )
    
    class Meta:
        verbose_name = _("上传记录")
        verbose_name_plural = _("上传记录")
        ordering = ['-created']
        
    def __str__(self):
        return f"{self.company_name} - {self.app_name} - {self.file_name}"
