{% extends "admin/base_site.html" %}
{% load static %}

{% block extrastyle %}{{ block.super }}
<link rel="stylesheet" href="{% static 'build_app/style/trigger_build.css' %}">
{# Add Select2 CSS #}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
{# Optional: Add Select2 Bootstrap 5 theme if needed, adjust path if using Bootstrap 4 #}
{# <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css"> #}
<style>
    /* Increase spacing between form rows */
    .form-row {
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">首页</a> &rsaquo;
    <a href="{% url 'admin:app_list' 'build_app' %}">Build App</a> &rsaquo;
    触发构建
</div>
{% endblock %}

{% block object-tools %}
<ul class="object-tools">
    <li>
        <a href="/build/logs/" class="viewsitelink">查看构建日志</a>
    </li>
</ul>
{% endblock %}

{% block content %}
<div class="trigger-build">

{% if messages %}
<ul class="messagelist">
    {% for message in messages %}
    <li{% if message.tags %} class="{{ message.tags }}" {% endif %}>{{ message }}</li>
        {% endfor %}
</ul>
{% endif %}

<form id="trigger-build-form" method="post" data-projects-url="{% url 'build_app:ajax_get_projects' %}" data-configs-url="{% url 'build_app:ajax_get_configs' %}">
    {% csrf_token %}
    <input type="hidden" name="app_config_ids" id="app-config-ids-hidden" value=""> 
    <fieldset class="module aligned">
        <h2>添加构建任务</h2>

        <div class="form-row">
            <label for="company-select">1. 选择公司:</label>
            <select class="form-input" name="company_id" id="company-select" required>
                <option value="">--- 请选择公司 ---</option>
                {% for company in companies %}
                <option value="{{ company.id }}" {% if request.POST.company_id == company.id|stringformat:"s" %}selected{% endif %}>{{ company.name }}</option>
                {% endfor %}
            </select>
        </div>

        <div class="form-row">
            <label>2. 选择应用配置 (可多选):</label>
            {# Wrap the dynamic list in a container for better alignment #}
            <div class="form-input"  id="app-config-list-container">
                <div id="app-config-list">
                    <small>--- 请先选择公司 ---</small>
                    <!-- Checkboxes will be loaded dynamically -->
                </div>
            </div>
        </div>

        <div class="form-row">
            <label for="project-select">3. 选择项目:</label>
            <select class="form-input" name="project_id" id="project-select" required disabled>
                <option value="">--- 请先选择公司 ---</option>
                <!-- Options will be loaded dynamically -->
            </select>
        </div>

        <div class="form-row">
            <label for="branch-input">4. 指定分支或标签 (可选):</label>
            <div class="form-input">
                <input type="text" name="branch_or_tag" id="branch-input" placeholder="留空则使用项目主分支"
                    value="{{ request.POST.branch_or_tag|default:'master' }}">
            </div>
        </div>
        
        <div class="form-row">
            <label for="auto-upload">5. 自动上传选项:</label>
            <div class="form-input">
                <div class="checkbox-container">
                    <input type="checkbox" name="auto_upload" id="auto-upload" value="1" 
                        {% if request.POST.auto_upload == '1' %}checked{% endif %}>
                    <label for="auto-upload" style="display: inline; font-weight: normal;">构建成功后自动上传到远程服务器（使用公司配置的服务器信息）</label>
                </div>
                <small class="form-text text-muted">如果公司没有配置服务器信息，自动上传将不会进行</small>
            </div>
        </div>
    </fieldset>

    <div class="submit-row" id="submit-row">
        <input type="submit" value="开始构建" class="default" id="submit-button" disabled>
        <span id="submit-help" style="margin-left: 10px; color: #ba2121;">请选择公司</span>
    </div>
</form>
</div>
{% endblock %}

{% block extrajs %}
    {{ block.super }} {# Load parent block's JS FIRST #}
    {# Add Select2 JS (ensure jQuery is loaded first, which admin/base_site.html does) #}
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    {# Load our custom JS AFTER jQuery and Select2 #}
    <script src="{% static 'build_app/js/trigger_build.js' %}"></script>
{% endblock %}