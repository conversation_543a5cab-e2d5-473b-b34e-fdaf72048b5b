# Cocos项目自动构建与打包系统

## 项目概述

本系统是一个Web应用，用于管理多个Cocos项目的构建和打包过程。它允许用户通过Web界面管理多个Cocos项目，为每个项目创建和保存不同的配置，执行构建和打包任务，并查看历史记录。系统将自动化Cocos项目构建和Android Gradle打包的过程，大大提高工作效率。

系统以公司为主体进行驱动，可以帮助公司打包至少五个游戏，游戏类型可以自行选择。每个公司下可以有多个打包配置信息，用户可以选择要打包的特定游戏。

## 系统架构

### 前端技术
- **框架**：Django + Django Template Language (DTL) 或 Jinja2 模板引擎
- **UI组件库**：TailwindCSS + HTMX
- **交互增强**：Alpine.js（轻量级JavaScript框架）

### 后端技术
- **语言/框架**：Python + Django
- **数据库**：Django ORM + PostgreSQL (生产环境推荐), SQLite (本地开发/测试可选)
- **异步任务队列**：Celery + Redis（处理长时间运行的构建任务）
- **文件存储**：本地存储或云存储（如AWS S3）用于APK文件
- **API支持 (可选)**: Django REST framework (DRF)

### 部署方案
- **容器化**：Docker + Docker Compose
- **Web服务器**：Gunicorn/uWSGI (WSGI服务器) + Nginx（反向代理）
- **进程管理**：Supervisor

## 功能模块

### 1. 公司管理
- 添加/编辑/删除公司信息
- 设置公司基本信息: 公司名称，合作类型，交付备注，交付资料路径, 聚合平台, 宝塔, 面板, 短信, 下载链接, 服务器等
- 公司列表展示与搜索

### 2. 项目管理
- 从git上拉取项目到本地
- 添加/编辑/删除Cocos项目
- 设置项目基本信息（名称、路径、版本、仓库地址、分支名称、项目本地存储路径、项目类型(Unity,Cocos,Android)，说明等）
- 项目列表展示与搜索

### 3. 配置管理
- 为每个公司创建多套配置，指向公司
- 配置包括：包名、软著名、版本号、icon图标, JKS路径，等
- 配置模板功能，便于快速创建新配置

### 4. 打包任务
- 创建打包任务（选择项目+配置）
- 任务队列管理（等待、执行中、已完成、失败）
- 实时查看打包日志（使用 Django Channels 或轮询实现）
- 取消正在执行的任务

### 5. 构建历史
- 查看历史构建记录
- 下载构建产物（APK文件）
- 构建统计（成功率、耗时等）

### 6. 用户管理
- 用户注册/登录（使用 Django 内建认证系统 `django.contrib.auth`）
- 权限管理（管理员、普通用户）

### 7. 系统配置
系统配置模型，用于存储全局配置：

```python
# models.py (示例，使用 Django ORM)
from django.db import models
from django.utils import timezone

class SystemConfig(models.Model):
    """
    系统配置模型，用于存储全局配置
    """
    key = models.CharField(max_length=100, unique=True, db_index=True, help_text="配置键")
    value = models.TextField(null=True, blank=True, help_text="配置值")
    description = models.TextField(null=True, blank=True, help_text="配置描述")
    is_secret = models.BooleanField(default=False, help_text="是否为敏感信息（如密码、token等）")
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.key

    @staticmethod
    def get_default_configs():
        """
        返回默认的系统配置 (数据通常在 migrations 或 management command 中初始化)
        """
        return [
            # 项目相关配置
            {"key": "projects_base_dir", "value": "D:/cocos_projects", "description": "项目默认存储根目录", "is_secret": False},

            # Git相关配置
            {"key": "gitee_api_url", "value": "https://gitee.com/api/v5", "description": "Gitee API地址", "is_secret": False},
            {"key": "gitee_access_token", "value": "your_gitee_access_token", "description": "Gitee访问令牌", "is_secret": True},

            # Cocos Creator相关配置
            {"key": "cocos_creator_path", "value": "C:/ProgramData/cocos/editors/Creator/3.8.5/CocosCreator.exe",
             "description": "Cocos Creator可执行文件路径", "is_secret": False},

            # Android SDK相关配置
            {"key": "android_sdk_path", "value": "C:/Users/<USER>/AppData/Local/Android/Sdk",
             "description": "Android SDK路径", "is_secret": False},
            {"key": "android_ndk_path", "value": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/23.2.8568313",
             "description": "Android NDK路径", "is_secret": False},
            {"key": "java_home", "value": "C:/Program Files (x86)/Java/jdk-********",
             "description": "Java Home路径", "is_secret": False},

            # 构建输出相关配置
            {"key": "build_output_dir", "value": "D:/cocos_builds", "description": "构建输出根目录", "is_secret": False},
        ]

    class Meta:
        db_table = "system_configs"
        verbose_name = "系统配置"
        verbose_name_plural = verbose_name
```

## 技术实现细节

### 构建流程自动化
1. **Cocos项目构建**
   - 调用Cocos命令行工具构建项目
   - 生成Android工程文件

2. **配置注入**
   - 修改AndroidManifest.xml（应用ID、权限等）
   - 修改build.gradle（版本号、签名配置等）
   - 替换资源文件（图标、启动图等）

3. **Gradle打包**
   - 调用Android Studio的Gradle命令执行打包
   - 支持不同打包类型（debug/release）
   - 支持多渠道打包

4. **结果处理**
   - 收集构建日志
   - 存储APK文件
   - 发送通知（可选，如邮件、企业微信等，可集成 Django 的邮件发送或第三方库）

### 数据模型设计
- 使用 Django ORM 定义模型 (`models.py`)
- 使用 Django Migrations 管理数据库结构变更

## 开发与部署计划

1. **环境准备**
   - 安装 Python 3.10+
   - 安装 `uv` (推荐的包和虚拟环境管理器: `pip install uv` 或参考官方文档)
   - 使用 `uv sync` 安装 `pyproject.toml` 文件中定义的项目依赖
   - 配置虚拟环境 (uv 会自动处理)
   - 配置数据库 (PostgreSQL/SQLite)
   - 配置Android SDK和Cocos命令行工具
   - 配置 Redis (用于 Celery)

2. **开发阶段**
   - 创建 Django 项目和应用
   - 定义 Django ORM 模型 (`models.py`)
   - 编写 Django 视图 (`views.py`) 和 URL 配置 (`urls.py`)
   - 创建 Django 模板 (templates)
   - 实现表单处理 (使用 `Django Forms`)
   - 配置 Celery 并实现构建任务 (`tasks.py`)
   - 实现实时日志查看 (使用 `Django Channels` 或前端轮询)
   - 编写管理后台 (使用 `Django Admin`)

3. **测试阶段**
   - 单元测试（Pytest 或 Django 内建 `unittest`）
   - 集成测试
   - 性能测试
   - 多项目并行构建测试

4. **部署阶段**
   - 准备服务器环境
   - 配置 Docker 容器
   - 设置 Nginx 反向代理
   - 配置 Gunicorn/uWSGI
   - 部署应用（使用 Docker Compose）
   - 运行数据库迁移 (`manage.py migrate`)
   - 收集静态文件 (`manage.py collectstatic`)

## 项目结构 (示例)

```
autobuild_project/
├── autobuild_project/        # Django项目配置目录
│   ├── __init__.py
│   ├── asgi.py
│   ├── celery.py             # Celery 配置
│   ├── settings.py           # 项目设置
│   ├── urls.py               # 项目主路由
│   └── wsgi.py
├── build_app/                # Django 应用 (示例: 构建相关)
│   ├── __init__.py
│   ├── admin.py
│   ├── apps.py
│   ├── migrations/
│   ├── models.py
│   ├── tasks.py              # Celery 任务
│   ├── tests.py
│   ├── urls.py               # 应用路由
│   └── views.py
├── core/                     # 可能包含一些共享的核心逻辑或模型
├── static/                   # 全局静态文件 (部署时用 collectstatic 收集)
├── templates/                # 全局模板
├── venv/                     # 虚拟环境 (建议加入 .gitignore)
├── docker/                   # Docker 配置
├── .env                      # 环境变量
├── .gitignore
├── docker-compose.yml        # Docker Compose 配置
├── manage.py                 # Django 管理脚本
├── pyproject.toml            # 项目依赖和构建配置 (使用 uv 管理)
└── README.md                 # 项目文档
```

## 扩展功能（未来可考虑）

- 构建通知（邮件、钉钉、企业微信等，使用 Django 邮件后端或第三方库）
- 构建触发器（Git提交自动触发，可通过 Webhooks 实现）
- 构建报告与分析
- iOS项目支持
- 集成测试平台（如蒲公英、Firebase等）
- 基于角色的访问控制（RBAC，使用 Django 权限系统）
- API密钥管理（用于CI/CD集成，可使用 DRF 或其他库）

## 常用命令

```bash
# 安装/同步依赖 (使用 uv 读取 pyproject.toml)
uv sync
# 或者
# uv pip install -r requirements.txt

# 运行数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建超级用户 (管理员)
python manage.py createsuperuser

# 启动开发服务器
python manage.py runserver

# 启动开发服务器，内网也可以访问
python manage.py runserver 0.0.0.0:8000

# 运行 Celery worker (需要先启动 Redis)
celery -A autobuild_project worker --loglevel=info --pool=solo

# 运行 Celery Beat (如果需要定时任务)
# celery -A autobuild_project beat --loglevel=info

# 进入 Django Shell
python manage.py shell

# 运行测试
python manage.py test

# 收集静态文件 (部署时用)
python manage.py collectstatic

# 创建迁移文件：
python manage.py makemigrations build_app

# 应用迁移到数据库：
python manage.py migrate

# 清空
redis-cli.exe FLUSHDB

# celer
python -m celery flower

