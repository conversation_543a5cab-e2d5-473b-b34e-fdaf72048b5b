"""
任务锁模块，用于防止同一项目的多个任务并行执行
"""
import time
import logging
import redis
import socket
import threading
import atexit
from django.conf import settings

logger = logging.getLogger(__name__)

# 保存当前服务器获取的锁，便于即使在异常情况下也能释放它们
_acquired_locks = set()
_lock_mutex = threading.Lock()

# 确保Redis连接
try:
    redis_client = redis.Redis.from_url(settings.CELERY_BROKER_URL)
    # 测试连接
    redis_client.ping()
    logger.info("成功连接到Redis")
except Exception as e:
    logger.error(f"无法连接到Redis: {e}")
    redis_client = None
    
# 获取当前机器标识
_hostname = socket.gethostname()
_server_id = f"{_hostname}:{id(threading.current_thread())}"

# 在进程退出时释放所有锁
def _cleanup_all_locks():
    """在进程退出时释放所有由此进程获取的锁"""
    if redis_client is None:
        return
        
    with _lock_mutex:
        locks_to_release = list(_acquired_locks)
    
    if locks_to_release:
        logger.warning(f"进程退出，正在释放{len(locks_to_release)}个项目锁")
        
        for project_id in locks_to_release:
            try:
                lock_key = f"project_lock:{project_id}"
                # 获取当前锁的值
                current_lock = redis_client.get(lock_key)
                if current_lock:
                    current_lock = current_lock.decode('utf-8')
                    # 只删除自己创建的锁
                    if current_lock.startswith(_server_id):
                        redis_client.delete(lock_key)
                        logger.info(f"进程退出时释放项目锁: {project_id}")
            except Exception as e:
                logger.error(f"进程退出时释放锁出错: {e}")

# 注册退出时的清理函数
atexit.register(_cleanup_all_locks)

def acquire_project_lock(project_id, timeout=300):
    """
    获取项目锁，防止同一项目的多个任务并行执行
    
    Args:
        project_id: 项目ID
        timeout: 锁超时时间（秒），默认5分钟
        
    Returns:
        是否成功获取锁
    """
    if redis_client is None:
        logger.warning("Redis连接不可用，无法获取锁，将继续执行")
        return True
        
    lock_key = f"project_lock:{project_id}"
    lock_value = f"{_server_id}:{time.time()}"
    
    # 检查锁是否在当前进程的缓存中（防止多次许可）
    with _lock_mutex:
        if project_id in _acquired_locks:
            logger.info(f"项目锁已经在本进程中获取，项目 ID: {project_id}")
            return True
    
    # 尝试获取锁，最多等待30秒
    max_retries = 30
    retry_count = 0
    
    while retry_count < max_retries:
        # 尝试获取锁（使用NX选项，只有当锁不存在时才设置）
        acquired = redis_client.set(lock_key, lock_value, ex=timeout, nx=True)
        if acquired:
            logger.info(f"成功获取项目锁: {project_id}")
            # 在本地缓存中记录锁
            with _lock_mutex:
                _acquired_locks.add(project_id)
            return True
        
        # 检查是否是死锁（超过2分钟的锁）
        if retry_count % 10 == 0:  # 每10次重试才检查一次，减少Redis负担
            try:
                # 获取锁信息
                current_lock = redis_client.get(lock_key)
                if current_lock:
                    current_lock = current_lock.decode('utf-8')
                    # 如果是当前服务器创建的锁，则强制释放
                    if current_lock.startswith(_server_id):
                        logger.warning(f"检测到当前服务器未正确释放的锁，强制释放: {project_id}")
                        redis_client.delete(lock_key)
                        # 下次循环会重新尝试获取
            except Exception as e:
                logger.error(f"检查锁状态时出错: {e}")
            
        # 如果获取锁失败，等待1秒后重试
        logger.info(f"项目 {project_id} 正被其他任务锁定，等待中... ({retry_count+1}/{max_retries})")
        time.sleep(1)
        retry_count += 1
    
    logger.warning(f"获取项目锁失败，达到最大重试次数: {project_id}")
    return False

def release_project_lock(project_id):
    """
    释放项目锁
    
    Args:
        project_id: 项目ID
    """
    try:
        # 从本地缓存中移除锁记录
        with _lock_mutex:
            if project_id in _acquired_locks:
                _acquired_locks.remove(project_id)
        
        if redis_client is None:
            return
            
        lock_key = f"project_lock:{project_id}"
        # 获取当前锁的值
        current_lock = redis_client.get(lock_key)
        if current_lock:
            current_lock = current_lock.decode('utf-8')
            # 只删除自己创建的锁，避免意外删除其他进程的锁
            if current_lock.startswith(_server_id):
                redis_client.delete(lock_key)
                logger.info(f"已释放项目锁: {project_id}")
            else:
                logger.warning(f"不删除项目锁，因为它属于另一个进程: {current_lock}")
                
    except Exception as e:
        logger.error(f"释放项目锁时出错: {e}")
        # 即使出错也尝试强制删除锁
        try:
            lock_key = f"project_lock:{project_id}"
            redis_client.delete(lock_key)
            logger.info(f"强制释放项目锁: {project_id}")
        except:
            pass
