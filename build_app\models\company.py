from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.db.models import JSO<PERSON>ield
from model_utils.models import TimeStampedModel
from django.conf import settings # To get the User model
from pathlib import Path

class Company(TimeStampedModel):
    """
    公司信息模型
    """
    name = models.CharField(max_length=200, unique=True, verbose_name="公司名称")
    cooperation_type = models.CharField(max_length=100, blank=True, null=True, verbose_name="合作类型")
    delivery_notes = models.TextField(blank=True, null=True, verbose_name="交付备注")
    delivery_material_path = models.CharField(max_length=500, blank=True, null=True, verbose_name="交付资料路径")
    aggregation_platform = models.CharField(max_length=200, blank=True, null=True, verbose_name="聚合平台")
    bt_panel_url = models.URLField(max_length=500, blank=True, null=True, verbose_name="宝塔面板URL")
    server_info = models.TextField(blank=True, null=True, verbose_name="服务器信息")
    sms_config = models.TextField(blank=True, null=True, verbose_name="短信配置")
    download_link = models.URLField(max_length=500, blank=True, null=True, verbose_name="游戏下载链接")
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    created_at = models.DateTimeField(default=timezone.now, editable=False)
    updated_at = models.DateTimeField(auto_now=True)
    company_data_path = models.CharField(
        _("公司资料路径"),
        max_length=512, 
        blank=True,
        null=True,
        editable=False,
        help_text=_("系统自动生成的公司资料存储路径")
    )

    def __str__(self):
        return self.name

    class Meta:
        db_table = "companies"
        verbose_name = "公司信息"
        verbose_name_plural = verbose_name
        ordering = ['name']
