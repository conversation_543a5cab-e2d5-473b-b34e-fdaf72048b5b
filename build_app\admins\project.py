"""
项目管理类
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from ..tasks.repo import sync_project_repo


class ProjectAdmin(admin.ModelAdmin):
    """项目管理类"""
    list_display = ('name', 'project_type', 'sync_status', 'modified')
    list_filter = ('project_type', 'sync_status')
    search_fields = ('name', 'description', 'repository_url')
    readonly_fields = ('created', 'modified')
    autocomplete_fields = []

    fields = (
        'name', 'description',
        'project_type', 'sync_status', 'repository_url', 'main_branch', 'local_path',
        ('created', 'modified')
    )

    actions = ['trigger_sync_repo_action']

    @admin.action(description='克隆/更新选中项目的代码')
    def trigger_sync_repo_action(self, request, queryset):
        """触发代码同步操作"""
        triggered_count = 0
        for project in queryset:
            project.sync_status = project.SyncStatus.PENDING
            project.sync_message = "已加入同步队列..."
            project.save(update_fields=['sync_status', 'sync_message'])
            sync_project_repo.delay(project.id)
            triggered_count += 1

        if triggered_count > 0:
            self.message_user(request, f"已为 {triggered_count} 个项目加入代码同步队列。", messages.SUCCESS)
        else:
            self.message_user(request, "没有选中任何项目。", messages.WARNING)
