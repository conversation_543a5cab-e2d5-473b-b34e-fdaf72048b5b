@echo off 
REM DjangoAutoBuild 数据库和媒体文件备份脚本 
set BACKUP_DIR=D:\sanlian\AIProject\DjangoAutoBuild\deploy_scripts\..\backup\%date:~0,4%-%date:~5,2%-%date:~8,2% 
mkdir %BACKUP_DIR% 2>nul 
echo 正在备份数据库和媒体文件到 %BACKUP_DIR% 
 
REM 备份SQLite数据库 
if exist "D:\sanlian\AIProject\DjangoAutoBuild\deploy_scripts\..\db.sqlite3" ( 
  copy "D:\sanlian\AIProject\DjangoAutoBuild\deploy_scripts\..\db.sqlite3" "%BACKUP_DIR%\db.sqlite3.bak" 
  echo [√] SQLite数据库已备份 
) 
 
REM 备份PostgreSQL数据库 - 如果使用PostgreSQL，请取消下面的注释并配置 
REM set PGPASSWORD=your_password 
REM pg_dump -U your_username -F c your_database > "%BACKUP_DIR%\db_backup.dump" 
 
REM 备份媒体文件 
xcopy /E /I /Y "D:\sanlian\AIProject\DjangoAutoBuild\deploy_scripts\..\media" "%BACKUP_DIR%\media" 
echo [√] 媒体文件已备份 
 
echo 备份完成! 
