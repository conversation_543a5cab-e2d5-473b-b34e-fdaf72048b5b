"""
配置生成模块，负责处理构建任务和生成配置文件。
"""
import os
import logging
from pathlib import Path

from celery import shared_task
from django.conf import settings

from ..models import BuildJob, Project, AppConfig
from ..utils import generate_config_files
from .base import run_command, check_job_canceled

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=1)
def process_build_job(self, build_job_id):
    """处理构建任务的入口点，直接启动Cocos构建流程。
    
    注意：新版本移除了CONFIG独立阶段，改为在每个构建阶段自行注入配置文件。
    这样能避免多个构建任务共享配置文件时的可能冲突。
    
    Args:
        build_job_id: 构建任务 ID
        
    Returns:
        True表示成功启动，False表示失败
    """
    # 先检查任务是否已被取消
    if check_job_canceled(build_job_id):
        logger.info(f"任务 {build_job_id} 已被取消，跳过构建流程")
        try:
            build_job = BuildJob.objects.get(pk=build_job_id)
            build_job.log += f"\n\n[系统通知] 任务已被取消，跳过构建流程\n"
            build_job.status = BuildJob.Status.CANCELED
            build_job.save(update_fields=['status', 'log'])
        except Exception as e:
            logger.error(f"更新已取消任务状态时出错: {e}")
        return f"任务 {build_job_id} 已被取消"
    
    try:
        from django.utils import timezone
        
        build_job = BuildJob.objects.get(pk=build_job_id)
        project = build_job.project
        app_config = build_job.app_config
        
        # 更新任务信息，直接设置状态为PREPARING，不再使用CONFIG阶段
        build_job.start_time = timezone.now()
        build_job.status = BuildJob.Status.PREPARING
        
        # 记录任务开始信息
        build_job.log = f"构建任务[{build_job_id}]开始初始化，时间:{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        build_job.log += f"项目：{project.name}\n"
        build_job.log += f"应用配置：{app_config.config_name if app_config else 'None'}\n"
        build_job.log += f"分支或标签：{build_job.branch_or_tag or project.main_branch}\n"
        build_job.log += f"────────────────────────────────────────────────────────\n"
        
        # 检查项目路径
        if not project.local_path or not os.path.isdir(project.local_path):
            error_msg = f"项目 '{project.name}' 的本地路径未设置或无效: {project.local_path}。请先同步仓库。"
            build_job.log += f"\n\n错误: {error_msg}\n"
        try:
            # 使用project.local_path替代未定义的project_local_path
            generate_config_files(build_job, project.local_path)
            build_job.log += f"配置文件生成成功。\n"
            build_job.save(update_fields=['log'])
        except Exception as config_err:
            logger.error(f"为构建任务 {build_job_id} 生成配置文件时出错: {config_err}")
            build_job.status = BuildJob.Status.FAILED
            build_job.log += f"\n错误：配置文件生成失败。\n{config_err}\n"
            build_job.save(update_fields=['status', 'log'])
            raise  # 重新引发异常，让 Celery 知道任务失败

        # --- 根据应用类型确定下一步操作 ---
        next_steps = _determine_build_strategy(build_job)
        build_job.log += next_steps
        build_job.save(update_fields=['log'])
        
        logger.info(f"构建任务 ID: {build_job_id} 的配置生成阶段完成")
        return f"构建任务 {build_job_id} 配置生成成功。"

    except BuildJob.DoesNotExist:
        logger.error(f"未找到 ID 为 {build_job_id} 的构建任务。")
        return f"未找到 ID 为 {build_job_id} 的构建任务。"
    except ValueError as ve:
        # 捕获特定的 ValueError 用于无效路径
        logger.error(f"处理构建任务 {build_job_id} 时出现 ValueError: {ve}")
        if build_job:
            build_job.status = BuildJob.Status.FAILED
            build_job.log = build_job.log + f"\n错误: {ve}" if build_job.log else str(ve)
            build_job.save(update_fields=['status', 'log'])
        return f"处理构建任务 {build_job_id} 失败: {ve}"
    except Exception as e:
        error_message = f"处理构建任务 {build_job_id} 时发生意外错误: {e}"
        logger.exception(error_message)  # 记录完整堆栈跟踪
        if build_job:
            build_job.status = BuildJob.Status.FAILED
            build_job.log = build_job.log + f"\n严重错误: {error_message}" if build_job.log else error_message
            build_job.save(update_fields=['status', 'log'])
        return f"处理构建任务 {build_job_id} 失败: {e}"


def _determine_build_strategy(build_job):
    """基于应用类型和配置确定构建策略，并更新任务链路控制状态。
    
    Args:
        build_job: 构建任务对象
        
    Returns:
        下一步操作的描述
    """
    from ..models import AppConfig
    from ..models.appConfig import BuildJob
    
    project = build_job.project
    app_config: AppConfig = build_job.app_config
    
    # 构建调试信息，记录任务链路状态
    debug_info = f"任务ID: {build_job.id}\n项目名称: {project.name}\n应用配置: {app_config.config_name if app_config else '无'}\n当前步骤: CONFIG -> ?\n时间戳: {build_job.created}\n本地路径: {project.local_path}"
    
    # 保存调试信息
    build_job.debug_info = debug_info
    build_job.save(update_fields=['debug_info'])
    
    if not app_config:
        # 更新任务步骤为完成
        build_job.current_step = BuildJob.BuildStep.COMPLETED
        build_job.save(update_fields=['current_step'])
        return "未指定应用配置，只执行配置文件注入。任务完成。"
    
    # 使用 get_app_type() 方法获取应用类型
    app_type = app_config.get_app_type()
    
    if app_type == AppConfig.ProjectType.COCOS:
        # 更新任务步骤为 Cocos 打包
        build_job.current_step = BuildJob.BuildStep.COCOS
        build_job.save(update_fields=['current_step'])
        
        # 更新调试信息
        debug_info += f"\n下一步骤: CONFIG -> COCOS\n项目类型: {app_type}"
        build_job.debug_info = debug_info
        build_job.save(update_fields=['debug_info'])
        
        # 触发 Cocos 构建
        from .cocos import build_cocos_project
        build_cocos_project.delay(build_job.id)
        return f"配置文件注入完成，任务已更新为 COCOS 步骤，启动 Cocos 构建任务...\n"
        
    elif app_type == AppConfig.ProjectType.ANDROID:
        # 更新任务步骤为 Android 构建
        build_job.current_step = BuildJob.BuildStep.ANDROID
        build_job.save(update_fields=['current_step'])
        
        # 更新调试信息
        debug_info += f"\n下一步骤: CONFIG -> ANDROID\n项目类型: {app_type}"
        build_job.debug_info = debug_info
        build_job.save(update_fields=['debug_info'])
        
        # 触发 Android 构建
        from .android import build_android_project
        build_android_project.delay(build_job.id)
        return f"配置文件注入完成，任务已更新为 ANDROID 步骤，启动 Android Gradle 构建任务...\n"
        
    else:
        # 更新任务步骤为完成
        build_job.current_step = BuildJob.BuildStep.COMPLETED
        build_job.save(update_fields=['current_step'])
        
        # 更新调试信息
        debug_info += f"\n下一步骤: CONFIG -> COMPLETED\n项目类型: {app_type}"
        build_job.debug_info = debug_info
        build_job.save(update_fields=['debug_info'])
        
        return f"配置文件注入完成，应用类型: '{app_type}'，无需额外构建步骤。任务完成。\n"
