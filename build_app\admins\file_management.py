"""
文件管理类
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.urls import path
from django.db import models

from ..admin_views import (
    file_manager_admin_view, company_files_admin_view, app_files_admin_view,
    download_file_admin_view, delete_file_admin_view, upload_file_admin_view,
    create_folder_admin_view
)
from ..remote_upload import upload_to_remote_server


class FileManagement(models.Model):
    """
    文件管理虚拟模型 - 用于在Admin界面中添加文件管理入口
    这个模型不会创建实际的数据库表，仅用于Admin UI显示
    """
    class Meta:
        verbose_name = '文件管理'
        verbose_name_plural = '文件管理'
        # 设置为False，这样Django不会尝试创建这个表
        managed = False
        # 给这个虚拟模型一个app_label
        app_label = 'build_app'


class FileManagementAdmin(admin.ModelAdmin):
    """
    文件管理Admin类 - 自定义Admin视图以支持文件管理功能
    """
    # 这个虚拟模型没有真实字段，所以我们不需要list_display

    def get_urls(self):
        """添加自定义URL以集成文件管理功能"""
        urls = super().get_urls()
        custom_urls = [
            # 主页 - 显示所有公司
            path('', self.admin_site.admin_view(file_manager_admin_view),
                name='build_app_filemanagement_changelist'),

            # 公司文件页
            path('company/<int:company_id>/',
                self.admin_site.admin_view(company_files_admin_view),
                name='file_manager_company'),

            # 应用文件页
            path('app/<int:app_config_id>/',
                self.admin_site.admin_view(app_files_admin_view),
                name='file_manager_app'),

            # 文件操作
            path('app/<int:app_config_id>/download/<path:path>',
                self.admin_site.admin_view(download_file_admin_view),
                name='file_manager_download'),

            path('app/<int:app_config_id>/delete/<path:path>',
                self.admin_site.admin_view(delete_file_admin_view),
                name='file_manager_delete'),

            path('app/<int:app_config_id>/upload/',
                self.admin_site.admin_view(upload_file_admin_view),
                name='file_manager_upload'),

            path('app/<int:app_config_id>/create-folder/',
                self.admin_site.admin_view(create_folder_admin_view),
                name='file_manager_create_folder'),

            # 远程上传
            path('app/<int:app_config_id>/remote-upload/<path:path>',
                self.admin_site.admin_view(upload_to_remote_server),
                name='file_manager_remote_upload'),
        ]
        return custom_urls + urls

    # 禁用所有常规的CRUD操作
    def has_add_permission(self, request):
        """禁止添加文件管理记录"""
        return False

    def has_change_permission(self, request, obj=None):
        """允许访问更改列表页面以显示文件管理功能"""
        return True  

    def has_delete_permission(self, request, obj=None):
        """禁止删除文件管理记录"""
        return False
