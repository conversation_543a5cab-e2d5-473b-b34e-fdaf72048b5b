@echo off
REM DjangoAutoBuild Celery Worker启动脚本

REM 检查虚拟环境是否存在
if not exist ".venv\Scripts\activate.bat" (
    echo 错误: 虚拟环境不存在，请先创建虚拟环境
    exit /b 1
)

REM 激活虚拟环境
echo 正在激活虚拟环境...
call ".venv\Scripts\activate.bat"

REM 检查celery是否已安装
pip show celery > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装celery和gevent...
    pip install celery gevent
    if %ERRORLEVEL% NEQ 0 (
        echo 错误: 安装celery和gevent失败
        exit /b 1
    )
)

REM 设置环境变量
set FORKED_BY_MULTIPROCESSING=1

REM 启动Celery Worker
echo 启动Celery Worker...
celery -A autobuild_project worker --pool=gevent --concurrency=2 --loglevel=info
