# autobuild_project/urls.py

from django.contrib import admin
from django.urls import path, include
from django.contrib.staticfiles.urls import staticfiles_urlpatterns # Import this helper
from django.conf import settings
from django.conf.urls.static import static

# 定义URL模式
urlpatterns = [
    # Django Admin站点
    path('admin/', admin.site.urls),
    
    # 构建应用URLs
    path('build_app/', include('build_app.urls', namespace='build_app')),
    
    # 内部兼容URL路径
    path('build/', include('build_app.urls', namespace='build')),
]

# Add this line to explicitly enable serving static files from apps by runserver
urlpatterns += staticfiles_urlpatterns()

# Note: The previous if settings.DEBUG: block for STATIC_ROOT remains removed.
