"""
取消任务的模型定义，用于跟踪已被取消的任务。
"""
from django.db import models
from django.utils import timezone


class CanceledTask(models.Model):
    """已取消的任务记录，用于确保任务不会被重新执行"""
    
    task_id = models.CharField("任务ID", max_length=100, unique=True)
    canceled_at = models.DateTimeField("取消时间", default=timezone.now)
    
    class Meta:
        verbose_name = "已取消任务"
        verbose_name_plural = "已取消任务"
        
    def __str__(self):
        return f"已取消任务 {self.task_id} ({self.canceled_at.strftime('%Y-%m-%d %H:%M:%S')})"
