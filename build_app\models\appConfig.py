from django.db import models
from django.utils.translation import gettext_lazy as _
from model_utils.models import TimeStampedModel
from django.conf import settings
from pathlib import Path
import logging
from django.utils.text import slugify
from PIL import Image

from ..utils import generate_jks_keystore # Assuming utils.py is one level up (in build_app/)
from .company import Company
from .project import Project

logger = logging.getLogger(__name__)

def app_config_icon_upload_path(instance, filename):
    """
    Generates upload path for AppConfig android_icon.
    Path: company_data/<safe_company_name>/<safe_app_name>/<original_filename>
    """
    # Accessing the properties directly from the instance
    safe_company = instance._safe_company_name_for_path
    safe_app = instance._safe_app_name_for_path
    return f'company_data/{safe_company}/{safe_app}/{filename}'
    
def app_config_jks_upload_path(instance, filename):
    """
    Generates upload path for AppConfig keystore JKS file.
    Path: company_data/<safe_company_name>/<safe_app_name>/keystore/<original_filename>
    """
    # Accessing the properties directly from the instance
    safe_company = instance._safe_company_name_for_path
    safe_app = instance._safe_app_name_for_path
    return f'company_data/{safe_company}/{safe_app}/keystore/{filename}'

class AppConfig(TimeStampedModel):
    """
    存储与特定公司相关的、可重用的应用构建配置参数。
    例如，包名、应用名、版本规则、图标、签名文件路径等。
    """
    
    class ProjectType(models.TextChoices):
        COCOS = 'cocos', _('Cocos 项目')
        ANDROID = 'android', _('Android 项目')
        WEB = 'web', _('Web 项目')
        OTHER = 'other', _('其他项目')
    class BuildType(models.TextChoices):
        DEBUG = 'DEBUG', _('调试版')
        RELEASE = 'RELEASE', _('发布版')

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE, 
        related_name='app_configs',
        verbose_name=_("所属公司")
    )
    config_name = models.CharField(
        _("配置名称"),
        max_length=100,
        help_text=_("易于识别的配置名称，例如 '官方发布版', '渠道 A Debug'")
    )
    app_name = models.CharField(_("应用显示名称"), max_length=100, blank=True)
    base_url = models.URLField(_("基础 URL (Cocos)"), blank=True, help_text=_("Cocos 使用的后台 API 地址"))
    baidu_location_ak = models.CharField(_("百度定位 AK"), max_length=100, blank=True)
    weixin_app_id = models.CharField(_("微信应用ID"), max_length=100, blank=True, help_text=_("微信应用的AppID"))
    weixin_app_secret = models.CharField(_("微信应用密钥"), max_length=100, blank=True, help_text=_("微信应用的AppSecret"))

    class CocosAdPlatform(models.TextChoices):
        ANY_THINK = 'any_think', _('塔库')
        CSJ = 'csj_ad', _('穿山甲'),
        SIGMOB = 'tobid_ad', _('sigmob'),
        NONE = 'none', _('无广告')
    cocos_ad_platform = models.CharField(
        _("广告平台 (Cocos)"),
        max_length=20,
        choices=CocosAdPlatform.choices,
        default=CocosAdPlatform.ANY_THINK,
        blank=False
    )

    class CocosLoginType(models.TextChoices):
        OTHER = '5', _('普通登录')
        GAME_PLATFORM = '4', _('游戏平台登录')
    cocos_login_type = models.CharField(
        _("登录类型 (Cocos)"),
        max_length=20,
        choices=CocosLoginType.choices,
        default=CocosLoginType.OTHER,
        blank=False
    )

    android_hide_icon = models.BooleanField(_("是否隐藏桌面图标 (Android)"), default=False)
    
    # 新增调试模式字段
    debug_log = models.BooleanField(
        _("调试模式"),
        default=False,
        help_text=_("是否启用调试日志模式")
    )
    
    # 新增代理码字段
    invite_code = models.CharField(
        _("代理码"),
        max_length=50,
        blank=True,
        help_text=_("应用使用的代理码")
    )

    extra_data = models.JSONField(
        _("额外配置数据"),
        default=dict,
        blank=True,
        help_text=_("用于存储不常用或临时的键值对，例如敏感信息（需注意安全风险）")
    )
    injection_rules = models.JSONField(
        _("注入规则"),
        default=list,
        blank=True,
        help_text=_("【技术人员配置】定义注入规则列表，格式：[{'project_type': '类型', 'template_path': '模板相对路径', 'target_path': '目标相对路径'}, ...]")
    )
    is_active = models.BooleanField(
        _("是否启用"),
        default=True,
        help_text=_("是否在触发构建时可选此配置")
    )
    description = models.TextField(_("描述"), blank=True, null=True)
    version_code_base = models.IntegerField(_("基础版本代码 (Android)"), default=0, help_text=_("例如 100 代表 1.0.0，530 代表 5.3.0"))
    application_id = models.CharField(_("应用包名 (Android)"), max_length=255, blank=True, help_text=_("例如：com.example.app"))
    keystore_base_name = models.CharField(_("密钥库基础名 (Android)"), max_length=100, blank=True, help_text=_("例如 'mykey'，将用于生成路径、别名和密码"))
    keystore_file = models.FileField(
        _("密钥库文件 (JKS)"),
        upload_to=app_config_jks_upload_path,
        blank=True,
        null=True,
        help_text=_("上传已存在的JKS文件。如果提供，将优先使用该文件而非自动生成。")
    )
    android_icon = models.ImageField(
        _("安卓应用图标"),
        upload_to=app_config_icon_upload_path,
        blank=True,
        null=True,
        help_text=_("上传应用的图标 (例如 .png, .jpg)。建议尺寸 512x512px。")
    )

    class Meta:
        verbose_name = _("应用配置")
        verbose_name_plural = _("应用配置")
        unique_together = ('company', 'config_name') 
        ordering = ['company', 'config_name']

    def __str__(self):
        return f"{self.company.name} - {self.config_name}"

    @property
    def _safe_company_name_for_path(self):
        if self.company and self.company.name:
            return slugify(self.company.name, allow_unicode=True)
        # Fallback, though company is a required field via ForeignKey
        return slugify("default-company", allow_unicode=True) 

    @property
    def _safe_app_name_for_path(self):
        if self.app_name and self.app_name.strip():
            return slugify(self.app_name, allow_unicode=True)
        return slugify("default-app", allow_unicode=True) # Fallback if app_name is empty

    @property
    def app_media_base_dir(self):
        media_root = Path(settings.MEDIA_ROOT)
        return media_root / 'company_data' / self._safe_company_name_for_path / self._safe_app_name_for_path

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs) # Call the "real" save() method first.

        # --- Android Icon MIPMAP Generation ---
        if self.android_icon and hasattr(self.android_icon, 'path') and self.android_icon.path:
            try:
                source_icon_full_path = Path(self.android_icon.path)
                if not source_icon_full_path.exists():
                    logger.warning(
                        f"AppConfig save: Android icon file does not exist at {source_icon_full_path} "
                        f"for AppConfig ID {self.id}. Skipping MIPMAP generation."
                    )
                else:
                    # Ensure the main app media directory exists
                    self.app_media_base_dir.mkdir(parents=True, exist_ok=True)

                    # MIPMAPs go into a 'res-{safe_app_name}' subdirectory of app_media_base_dir
                    res_app_folder_name = f"res-{self._safe_app_name_for_path}"
                    mipmap_base_output_dir = self.app_media_base_dir / res_app_folder_name
                    mipmap_base_output_dir.mkdir(parents=True, exist_ok=True)
                    
                    logger.info(f"AppConfig save: Preparing to generate MIPMAPs in {mipmap_base_output_dir}")

                    densities = {
                        "mipmap-mdpi": 48,    # 48x48 px
                        "mipmap-hdpi": 72,    # 72x72 px
                        "mipmap-xhdpi": 96,   # 96x96 px
                        "mipmap-xxhdpi": 144, # 144x144 px
                        "mipmap-xxxhdpi": 192, # 192x192 px
                    }

                    img = Image.open(source_icon_full_path)
                    if img.mode != 'RGBA': # Ensure alpha channel for transparency
                        img = img.convert('RGBA')

                    for density_folder, size in densities.items():
                        density_target_path = mipmap_base_output_dir / density_folder
                        density_target_path.mkdir(exist_ok=True)
                        
                        resized_img = img.resize((size, size), Image.Resampling.LANCZOS)
                        target_icon_file_path = density_target_path / "ic_launcher.png"
                        resized_img.save(target_icon_file_path)
                        logger.info(f"AppConfig save: Generated Android icon: {target_icon_file_path}")
                    
                    logger.info(
                        f"AppConfig save: Successfully generated MIPMAP icons in {mipmap_base_output_dir} "
                        f"for AppConfig ID {self.id}"
                    )

            except ImportError:
                logger.error(
                    "AppConfig save: Pillow (PIL) library is not installed. "
                    f"Cannot generate MIPMAP icons for AppConfig ID {self.id}."
                )
            except Exception as e:
                logger.error(
                    f"AppConfig save: Error generating MIPMAP icons for AppConfig ID {self.id} "
                    f"(icon: {self.android_icon.name if self.android_icon else 'N/A'}): {e}",
                    exc_info=True
                )
        # --- End of Android Icon MIPMAP Generation ---

        # --- JKS Generation ---
        try:
            from ..utils import generate_jks_keystore
            import os

            if self.keystore_base_name and self.keystore_base_name.strip():
                # 生成参数
                base_name = self.keystore_base_name.strip()

                # 首先检查是否已经有JKS文件，按照优先级检查
                jks_filename = f"{base_name}.jks"
                old_jks_path = f"D:/Cocos/CocosForAndroid/AndroidAppJKSs/{jks_filename}"

                # 确保应用媒体目录存在
                self.app_media_base_dir.mkdir(parents=True, exist_ok=True)
                output_jks_path = self.app_media_base_dir / jks_filename

                # 检查是否需要生成JKS文件
                need_generate_jks = True

                # 1. 检查旧路径是否存在JKS文件
                if os.path.exists(old_jks_path):
                    logger.info(f"AppConfig save: 找到旧路径JKS文件: {old_jks_path}, 不需要生成新的")
                    need_generate_jks = False

                # 2. 检查是否有上传的JKS文件
                elif self.keystore_file and self.keystore_file.name:
                    logger.info(f"AppConfig save: 使用上传的JKS文件: {self.keystore_file.name}, 不需要生成新的")
                    need_generate_jks = False

                # 3. 检查生成路径是否已经存在JKS文件
                elif os.path.exists(output_jks_path):
                    logger.info(f"AppConfig save: 找到现有JKS文件: {output_jks_path}, 不需要生成新的")
                    need_generate_jks = False

                # 如果需要生成新的JKS文件
                if need_generate_jks:
                    # 如果公司名称为空，则使用已定义的ID
                    dname_company_name = self.company.name 

                    alias = base_name
                    password = f"{base_name}168"
                    # CN=公司名, OU=mdmd, O=mdmd, L=gz, ST=gz, C=zh
                    dname = f"CN={dname_company_name}, OU=mdmd, O=mdmd, L=gz, ST=gz, C=zh"
                    
                    logger.info(f"AppConfig save: 尝试生成JKS文件: '{self.app_name}' 使用别名 '{alias}' 位置 {output_jks_path}")
                    
                    success = generate_jks_keystore(
                        alias=alias,
                        store_password=password,
                        key_password=password, # 密钥密码与密钥库密码相同
                        dname=dname,
                        output_jks_path=output_jks_path
                    )
                    if success:
                        logger.info(f"AppConfig save: 成功生成JKS文件位置 {output_jks_path} 对应AppConfig ID {self.id}")
                    else:
                        logger.error(f"AppConfig save: Failed to generate JKS file at {output_jks_path} for AppConfig ID {self.id}")

        except Exception as e:
            logger.error(
                f"AppConfig save: Error generating JKS for AppConfig ID {self.id} "
                f"(keystore_base_name: {self.keystore_base_name}): {e}",
                exc_info=True
            )
        # --- End of JKS Generation ---

    def get_app_type(self):
        """
        根据配置属性判断应用类型
        
        Returns:
            ProjectType 枚举值
        """
        if self.injection_rules:
            for rule in self.injection_rules:
                if isinstance(rule, dict) and 'project_type' in rule:
                    project_type = rule.get('project_type', '')
                    if 'cocos' in project_type.lower():
                        return self.ProjectType.COCOS
                    elif 'android' in project_type.lower():
                        return self.ProjectType.ANDROID
                    elif 'web' in project_type.lower():
                        return self.ProjectType.WEB
        
        if self.base_url or self.cocos_ad_platform != self.CocosAdPlatform.NONE:
            return self.ProjectType.COCOS
        
        if self.application_id or self.keystore_base_name:
            return self.ProjectType.ANDROID
        
        return self.ProjectType.OTHER

    @property
    def android_use_csj_sdk(self):
        return self.cocos_ad_platform == self.CocosAdPlatform.CSJ

    @property
    def is_ak(self):
        return not (self.baidu_location_ak is None or not self.baidu_location_ak)

class BuildJob(TimeStampedModel):
    """
    代表一次具体的构建任务实例。
    关联项目源码、应用配置，并跟踪构建过程的状态和结果。
    """
    class Status(models.TextChoices):
        PENDING = 'PENDING', _('等待中')
        PREPARING = 'PREPARING', _('准备环境')
        BUILDING = 'BUILDING', _('构建中')
        PACKAGING = 'PACKAGING', _('打包中')
        UPLOADING = 'UPLOADING', _('上传中')
        SUCCESS = 'SUCCESS', _('成功')
        FAILED = 'FAILED', _('失败')
        CANCELED = 'CANCELED', _('已取消')
        
    class BuildStep(models.TextChoices):
        CONFIG = 'CONFIG', _('配置注入')
        COCOS = 'COCOS', _('Cocos打包')
        ANDROID = 'ANDROID', _('Android构建')
        COMPLETED = 'COMPLETED', _('完成')

    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE, 
        related_name='build_jobs',
        verbose_name=_("项目源码")
    )
    app_config = models.ForeignKey(
        AppConfig,
        on_delete=models.PROTECT, 
        related_name='build_jobs',
        verbose_name=_("应用配置")
    )
    branch_or_tag = models.CharField(
        _("分支或标签"),
        max_length=255,
        blank=True,
        null=True,
        help_text=_("要构建的 Git 分支或标签 (留空则使用项目主分支)")
    )
    commit_id = models.CharField(
        _("Commit ID"),
        max_length=40, 
        blank=True,
        null=True,
        editable=False, 
        help_text=_("实际用于构建的 Git Commit 哈希值")
    )
    status = models.CharField(
        _("状态"),
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING,
        db_index=True 
    )
    log = models.TextField(
        _("构建日志"),
        blank=True,
        null=True,
        help_text=_("构建过程的详细日志")
    )
    start_time = models.DateTimeField(
        _("开始时间"),
        blank=True,
        null=True,
        editable=False
    )
    end_time = models.DateTimeField(
        _("结束时间"),
        blank=True,
        null=True,
        editable=False
    )
    log_message = models.TextField(
        _("日志信息"),
        blank=True,
        null=True,
        editable=False,
        help_text=_("构建过程的关键日志或错误信息")
    )
    output_file_path = models.CharField(
        _("产物路径"),
        max_length=1024, 
        blank=True,
        null=True,
        editable=False,
        help_text=_("构建成功后的产物文件路径或 URL")
    )
    
    current_step = models.CharField(
        _("当前步骤"),
        max_length=20,
        choices=BuildStep.choices,
        default=BuildStep.CONFIG,
        help_text=_("构建流程中的当前步骤，用于跟踪执行进度")
    )
    
    debug_info = models.TextField(
        _("调试信息"),
        blank=True,
        null=True,
        help_text=_("用于存储详细的调试信息，便于排查问题")
    )
    auto_upload = models.BooleanField(
        _("自动上传"),
        default=False,
        help_text=_("构建成功后是否自动上传到远程服务器")
    )
    triggered_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        blank=True,
        null=True,
        related_name='triggered_build_jobs',
        verbose_name=_("触发者"),
        editable=False
    )

    class Meta:
        verbose_name = _("构建任务")
        verbose_name_plural = _("构建任务")
        ordering = ['-created'] 

    def __str__(self):
        return f"Job #{self.id} ({self.project.name} - {self.app_config.config_name})"

    # Optional: Add properties for duration, etc. later if needed
    # @property
    # def duration(self):
    #     if self.start_time and self.end_time:
    #         return self.end_time - self.start_time
    #     return None
