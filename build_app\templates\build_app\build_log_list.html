{% extends "admin/base_site.html" %}
{% load static %}

{% block extrastyle %}{{ block.super }}
<style>
    /* 主要容器样式 */
    .card {
        margin-bottom: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        border: 0;
    }
    
    .card-header {
        background-color: rgba(0, 0, 0, 0.03);
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .card-header h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 500;
    }
    
    .card-body {
        padding: 1.25rem;
    }
    
    /* 表格样式 */
    .table {
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
        border-collapse: collapse;
    }
    
    .table th {
        border-top: 0;
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
        padding: 0.75rem;
        font-weight: 500;
    }
    
    .table td {
        padding: 0.75rem;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
    }
    
    .table tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }
    
    /* 状态颜色 */
    .badge {
        display: inline-block;
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 500;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }
    
    .badge-secondary { background-color: #6c757d; color: #fff; }
    .badge-info { background-color: #17a2b8; color: #fff; }
    .badge-success { background-color: #28a745; color: #fff; }
    .badge-danger { background-color: #dc3545; color: #fff; }
    
    /* 查看日志按钮 */
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        vertical-align: middle;
        user-select: none;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    
    .btn-primary:hover {
        color: #fff;
        background-color: #0069d9;
        border-color: #0062cc;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.76562rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
</style>
{% endblock %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">首页</a></li>
    <li class="breadcrumb-item"><a href="{% url 'admin:app_list' 'build_app' %}">构建应用</a></li>
    <li class="breadcrumb-item active">构建日志列表</li>
</ol>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3>构建日志列表</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>项目</th>
                                    <th>配置</th>
                                    <th>当前步骤</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for job in build_jobs %}
                                <tr>
                                    <td>{{ job.id }}</td>
                                    <td>{{ job.project.name }}</td>
                                    <td>{{ job.app_config.config_name }}</td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ job.get_current_step_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if job.status == 'PENDING' %}
                                            <span class="badge badge-secondary">{{ job.get_status_display }}</span>
                                        {% elif job.status == 'BUILDING' %}
                                            <span class="badge badge-info">{{ job.get_status_display }}</span>
                                        {% elif job.status == 'SUCCESS' %}
                                            <span class="badge badge-success">{{ job.get_status_display }}</span>
                                        {% elif job.status == 'FAILED' %}
                                            <span class="badge badge-danger">{{ job.get_status_display }}</span>
                                        {% else %}
                                            {{ job.get_status_display }}
                                        {% endif %}
                                    </td>
                                    <td>{{ job.created|date:"Y-m-d H:i:s" }}</td>
                                    <td>
                                        <a href="{% url 'build_app:build_log_detail' job.id %}" class="btn btn-primary btn-sm">
                                            查看日志
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无构建记录</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}