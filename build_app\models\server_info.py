"""
服务器信息模型 - 存储公司关联的服务器信息
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from model_utils.models import TimeStampedModel
from .company import Company

class ServerInfo(TimeStampedModel):
    """
    服务器信息模型 - 记录上传目标服务器的信息
    """
    company = models.ForeignKey(
        Company, 
        on_delete=models.CASCADE, 
        related_name='server_infos',
        verbose_name=_("所属公司")
    )
    name = models.CharField(
        _("服务器名称"), 
        max_length=100, 
        help_text=_("用于标识的服务器名称")
    )
    host = models.CharField(
        _("服务器地址"), 
        max_length=255, 
        help_text=_("服务器IP地址或域名")
    )
    port = models.IntegerField(
        _("服务器端口"), 
        default=22,
        help_text=_("SSH端口号，默认为22")
    )
    username = models.Char<PERSON>ield(
        _("用户名"), 
        max_length=100,
        help_text=_("登录用户名")
    )
    password = models.CharField(
        _("密码"), 
        max_length=255,
        help_text=_("登录密码")
    )
    remote_path = models.CharField(
        _("远程路径"), 
        max_length=500,
        help_text=_("文件上传的远程目录路径")
    )
    is_default = models.BooleanField(
        _("是否默认"), 
        default=False,
        help_text=_("是否为该公司的默认服务器")
    )
    description = models.TextField(
        _("描述"), 
        blank=True, 
        null=True,
        help_text=_("服务器用途描述")
    )
    last_used = models.DateTimeField(
        _("最后使用时间"), 
        blank=True, 
        null=True,
        help_text=_("最后一次上传文件的时间")
    )
    
    def __str__(self):
        return f"{self.company.name} - {self.name} ({self.host})"
    
    class Meta:
        db_table = "server_infos"
        verbose_name = _("服务器信息")
        verbose_name_plural = _("服务器信息")
        ordering = ['company', '-is_default', 'name']
        unique_together = [('company', 'host', 'port', 'username')]
