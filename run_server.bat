@echo off
REM DjangoAutoBuild Web服务启动脚本

REM 检查虚拟环境是否存在
if not exist ".venv\Scripts\activate.bat" (
    echo 错误: 虚拟环境不存在，请先创建虚拟环境
    exit /b 1
)

REM 激活虚拟环境
echo 正在激活虚拟环境...
call ".venv\Scripts\activate.bat"

REM 检查waitress是否已安装
pip show waitress > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装waitress...
    pip install waitress
    if %ERRORLEVEL% NEQ 0 (
        echo 错误: 安装waitress失败
        exit /b 1
    )
)

REM 启动服务器
echo 启动Web服务器...
python -m waitress --port=8000 autobuild_project.wsgi:application
