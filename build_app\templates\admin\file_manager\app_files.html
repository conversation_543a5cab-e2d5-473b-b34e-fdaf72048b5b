{% extends "admin/base.html" %}
{% load i18n admin_urls jazzmin %}
{% load file_filters %}

{% block bodyclass %}{{ block.super }} dashboard{% endblock %}
{% load i18n %}

{% block extrastyle %}{{ block.super }}
<style>
    .file-manager-container {
        margin: 20px 0;
    }

    .file-item {
        border-bottom: 1px solid #eee;
        padding: 10px 0;
    }

    .file-item:hover {
        background-color: #f8f9fa;
    }

    .file-actions {
        visibility: hidden;
    }

    .file-item:hover .file-actions {
        visibility: visible;
    }

    .preview-image {
        max-width: 100%;
        max-height: 300px;
        object-fit: contain;
    }

    #upload-progress {
        display: none;
    }

    .breadcrumb-container {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .back-btn {
        margin-right: 10px;
    }

    .file-card {
        margin-top: 20px;
        border-radius: 5px;
    }

    #content-main {
        width: 100%;
    }

    /* 自定义模态框样式 */
    .custom-modal-wrapper {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
    }

    .custom-modal-wrapper.show {
        display: block;
    }

    .custom-modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 10000;
    }

    .custom-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    }

    .custom-modal-dialog {
        width: 100%;
        max-width: 500px;
        margin: 1.75rem auto;
        z-index: 10002;
    }

    .custom-modal-content {
        position: relative;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, .2);
        border-radius: 0.3rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        width: 100%;
    }

    .custom-modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .custom-modal-title {
        margin: 0;
        font-size: 1.25rem;
    }

    .custom-close-btn {
        background: transparent;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
    }

    .custom-modal-body {
        padding: 1rem;
    }

    .custom-modal-footer {
        display: flex;
        justify-content: flex-end;
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        gap: 0.5rem;
    }

    .alert-copy-success {
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        z-index: 10003;
    }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a> &rsaquo;
    <a href="{% url 'admin:build_app_filemanagement_changelist' %}">文件管理</a> &rsaquo;
    <a href="{% url 'admin:file_manager_company' app_config.company.id %}">{{ app_config.company.name }}</a> &rsaquo;
    {{ app_config.config_name }}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="file-manager-container">
        <h1>{{ app_config.config_name }} - 文件管理</h1>
        <p>
            应用名称: {{ app_config.app_name|default:"未设置" }}<br>
            包名: {{ app_config.application_id|default:"未设置" }}
        </p>

        <div class="nav-actions mb-4">
            <a href="{% url 'admin:file_manager_company' app_config.company.id %}"
                class="btn btn-secondary btn-sm back-btn">
                <i class="fas fa-arrow-left"></i> 返回应用列表
            </a>
        </div>


        <div class="card file-card">
            <div class="card-header">
                <h5>文件列表</h5>
                <small class="text-muted">下载地址: {{ download_link }}</small>
            </div>
            <div class="card-body">
                {% if files %}
                <div class="list-group">
                    {% for file in files %}
                    <div class="file-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="{% if file.is_image %}fas fa-file-image{% else %}fas fa-file{% endif %} me-2"></i>
                            <a href="#" class="file-name" data-path="{{ file.path }}"
                                data-is-image="{{ file.is_image|yesno:'true,false' }}">
                                {{ file.name }}
                            </a>
                            <small class="text-muted ms-2">{{ file.size }}</small>
                        </div>
                        {% if file.name|lower|endswith:'.apk' %}
                        <div>
                            <a href="#" class="file-name" data-path="{{ file.path }}"
                                data-is-image="{{ file.is_image|yesno:'true,false' }}">
                                {{download_link}}/{{ file.name }}
                            </a>
                        </div>
                        {% endif %}
                        <div class="file-actions">
                            {% if file.name|lower|endswith:'.apk' %}
                            <button class="btn btn-sm btn-outline-success remote-upload-btn" data-path="{{ file.path }}"
                                data-name="{{ file.name }}">
                                <i class="fas fa-upload"></i> 上传
                            </button>
                            <a href="{{download_link}}/{{ file.name }}"
                                class="btn btn-sm btn-outline-primary" download>
                                <i class="fas fa-download"></i> 远程下载
                            </a>
                             <button class="btn btn-sm btn-outline-info copy-download-link-btn"
                                data-name="{{ file.name }}">
                                <i class="fas fa-copy"></i> 复制链接
                            </button>
                            {% endif %}
                             <a href="{% url 'admin:file_manager_download' app_config.id file.path %}"
                                class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download"></i> 本地下载
                            </a>
                            <button class="btn btn-sm btn-outline-danger delete-file" data-path="{{ file.path }}"
                                data-name="{{ file.name }}">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    暂无文件。您可以点击"上传文件"按钮添加文件。
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 远程上传模态框 -->
<div class="custom-modal-wrapper" id="remoteUploadModalWrapper">
    <div class="custom-modal-backdrop"></div>
    <div class="custom-modal" id="remoteUploadModal">
        <div class="custom-modal-dialog">
            <div class="custom-modal-content">
                <div class="custom-modal-header">
                    <h5 class="custom-modal-title">上传APK到远程服务器</h5>
                    <button type="button" class="custom-close-btn" id="closeModalBtn">&times;</button>
                </div>
                <div class="custom-modal-body">
                    <form id="remoteUploadForm">
                        <input type="hidden" id="uploadFilePath" name="file_path" value="">

                        <div class="mb-3">
                            <label for="serverHost" class="form-label">服务器地址</label>
                            <input type="text" class="form-control" id="serverHost" name="server_host"
                                value="{{ server_ip }}" required>
                            {% if server_ip %}
                            <small class="form-text text-success">已自动填充公司服务器地址</small>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="serverPort" class="form-label">端口</label>
                            <input type="number" class="form-control" id="serverPort" name="server_port" 
                                value="{{ server_port }}" required>
                            {% if server_port != 22 %}
                            <small class="form-text text-success">已自动填充公司服务器端口</small>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="serverUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="serverUsername" name="server_username"
                                value="{{ server_username }}" required>
                            {% if server_username != 'root' %}
                            <small class="form-text text-success">已自动填充公司服务器用户名</small>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="serverPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="serverPassword" name="server_password"
                                value="{{ server_password }}" required>
                            {% if server_password %}
                            <small class="form-text text-success">已自动填充公司服务器密码</small>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="remotePath" class="form-label">上传目录</label>
                            <input type="text" class="form-control" id="remotePath" name="remote_path"
                                value="{{ remote_path }}" required>
                            {% if remote_path != '/www/wwwroot/default' %}
                            <small class="form-text text-success">已自动填充公司服务器上传路径</small>
                            {% endif %}
                            <div class="help">
                                推荐路径: <code>/www/wwwroot/default</code>
                            </div>
                        </div>
                    </form>

                    <div class="alert alert-info" id="uploadInfo" style="display: none;"></div>
                    <div class="alert alert-success" id="uploadSuccess" style="display: none;"></div>
                    <div class="alert alert-danger" id="uploadError" style="display: none;"></div>

                    <div class="progress mt-3" id="uploadProgress" style="display: none;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                            style="width: 0%"></div>
                    </div>
                </div>
                <div class="custom-modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelModalBtn">取消</button>
                    <button type="button" class="btn btn-primary" id="startUploadBtn">开始上传</button>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extrajs %}{{ block.super }}
<script>
    $(document).ready(function () {
        // 复制到剪贴板函数
        function copyToClipboard(text) {
            // 显示成功消息的函数
            function showSuccessMessage() {
                alert('已复制到剪贴板: ' + text);
            }

            // 显示错误消息的函数
            function showErrorMessage() {
                alert('复制失败，请手动复制: ' + text);
            }

            // 尝试使用现代API
            try {
                // 检查navigator.clipboard是否可用
                if (navigator && navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
                    navigator.clipboard.writeText(text)
                        .then(showSuccessMessage)
                        .catch(function (err) {
                            console.warn('Clipboard API失败，尝试备用方法', err);
                            useFallbackMethod();
                        });
                } else {
                    // 如果Clipboard API不可用，使用备用方法
                    console.warn('Clipboard API不可用，尝试备用方法');
                    useFallbackMethod();
                }
            } catch (e) {
                console.warn('尝试使用Clipboard API时出错，尝试备用方法', e);
                useFallbackMethod();
            }

            // 备用复制方法
            function useFallbackMethod() {
                try {
                    const textarea = document.createElement('textarea');
                    textarea.value = text;
                    // 确保textarea在视口之外
                    textarea.style.position = 'fixed';
                    textarea.style.left = '-9999px';
                    textarea.style.top = '-9999px';
                    document.body.appendChild(textarea);
                    textarea.focus();
                    textarea.select();

                    const successful = document.execCommand('copy');
                    document.body.removeChild(textarea);

                    if (successful) {
                        showSuccessMessage();
                    } else {
                        showErrorMessage();
                    }
                } catch (err) {
                    console.error('备用复制方法失败:', err);
                    showErrorMessage();
                }
            }
        }

        // 设置CSRF令牌
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        const csrftoken = getCookie('csrftoken');

        $('.copy-download-link-btn').click(function () {
            const name = $(this).data('name');
            const downloadLink = '{{ download_link }}' + '/' + name;
            copyToClipboard(downloadLink);
        });

        // 文件删除
        $('.delete-file').click(function () {
            const path = $(this).data('path');
            const name = $(this).data('name');

            if (confirm(`确定要删除文件 "${name}" 吗？`)) {
                $.ajax({
                    url: "{% url 'admin:file_manager_delete' app_config.id 'dummy_path' %}".replace('dummy_path', path),
                    type: 'POST',
                    headers: { 'X-CSRFToken': csrftoken },
                    success: function (data) {
                        if (data.success) {
                            // 删除成功，刷新页面
                            window.location.reload();
                        } else {
                            alert('删除失败: ' + data.error);
                        }
                    },
                    error: function () {
                        alert('删除请求失败，请稍后再试。');
                    }
                });
            }
        });

        // 自定义模态框控制
        function showModal(modalId) {
            $('#' + modalId + 'Wrapper').addClass('show');
        }

        function hideModal(modalId) {
            $('#' + modalId + 'Wrapper').removeClass('show');
        }

        // 远程上传
        $('.remote-upload-btn').click(function () {
            const path = $(this).data('path');
            const name = $(this).data('name');

            // 设置文件路径
            $('#uploadFilePath').val(path);

            // 重置表单和提示
            // 注意：不要重置表单，因为我们已经预填充了服务器信息
            // $('#remoteUploadForm')[0].reset();
            $('#uploadInfo, #uploadSuccess, #uploadError').hide();
            $('#uploadProgress').hide().find('.progress-bar').css('width', '0%');

            // 显示自定义模态框
            showModal('remoteUploadModal');
        });

        // 关闭模态框
        $('#closeModalBtn, #cancelModalBtn').click(function () {
            hideModal('remoteUploadModal');
        });

        // 点击背景关闭模态框
        $('.custom-modal-backdrop').click(function () {
            hideModal('remoteUploadModal');
        });

        // 阻止点击模态框内容时关闭
        $('.custom-modal-content').click(function (e) {
            e.stopPropagation();
        });

        // 开始上传
        $('#startUploadBtn').click(function () {
            // 验证表单
            const form = $('#remoteUploadForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // 获取表单数据
            const filePath = $('#uploadFilePath').val();
            const serverHost = $('#serverHost').val();
            const serverPort = $('#serverPort').val();
            const serverUsername = $('#serverUsername').val();
            const serverPassword = $('#serverPassword').val();
            const remotePath = $('#remotePath').val();

            // 显示上传中提示
            $('#uploadInfo').text('正在上传文件，请稍候...').show();
            $('#uploadSuccess, #uploadError').hide();
            $('#uploadProgress').show().find('.progress-bar').css('width', '50%');

            // 禁用按钮
            $('#startUploadBtn').prop('disabled', true).text('上传中...');

            const url = "{% url 'admin:file_manager_remote_upload' app_config.id 'dummy_path' %}"
                .replace('dummy_path', filePath);

            // 发送上传请求
            $.ajax({
                url: url,
                type: 'POST',
                headers: { 'X-CSRFToken': csrftoken },
                data: {
                    server_host: serverHost,
                    server_port: serverPort,
                    server_username: serverUsername,
                    server_password: serverPassword,
                    remote_path: remotePath
                },
                success: function (data) {
                    $('#uploadProgress').find('.progress-bar').css('width', '100%');

                    if (data.success) {
                        $('#uploadInfo').hide();
                        $('#uploadSuccess').text(data.message).show();

                        // 3秒后关闭模态框
                        setTimeout(function () {
                            hideModal('remoteUploadModal');
                        }, 3000);
                    } else {
                        $('#uploadInfo').hide();
                        $('#uploadError').text('上传失败: ' + data.error).show();
                    }
                },
                error: function (xhr) {
                    $('#uploadProgress').hide();
                    $('#uploadInfo').hide();
                    $('#uploadError').text('上传请求失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : '未知错误')).show();
                },
                complete: function () {
                    // 恢复按钮状态
                    $('#startUploadBtn').prop('disabled', false).text('开始上传');
                }
            });
        });
    });
</script>
{% endblock %}