#!/usr/bin/env python
"""
Flower启动脚本 - Celery任务监控工具

使用方法:
    python start_flower.py

默认配置:
    - 监听端口: 5555
    - 监控地址: http://localhost:5555
"""
import os
import sys
import subprocess
from pathlib import Path

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'autobuild_project.settings')

# Flower配置
FLOWER_PORT = 5555
BROKER_URL = "redis://localhost:6379/0"

def start_flower():
    """启动Flower监控服务"""
    print(f"正在启动Flower监控服务，端口: {FLOWER_PORT}")
    print(f"监控地址: http://localhost:{FLOWER_PORT}")
    print("按Ctrl+C停止服务")
    print(f"使用的Broker URL: {BROKER_URL}")
    
    # 使用subprocess运行flower命令
    cmd = [
        sys.executable,  # 当前Python解释器
        "-m",           # 模块模式
        "flower.command.run_command",  # flower的直接启动模块
        f"--port={FLOWER_PORT}",
        f"--broker={BROKER_URL}"
    ]
    
    try:
        # 启动Flower进程
        print(f"执行命令: {' '.join(cmd)}")
        process = subprocess.run(cmd)
        return process.returncode
    except KeyboardInterrupt:
        print("\nFlower服务已停止")
        return 0
    except Exception as e:
        print(f"启动Flower时出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(start_flower())
