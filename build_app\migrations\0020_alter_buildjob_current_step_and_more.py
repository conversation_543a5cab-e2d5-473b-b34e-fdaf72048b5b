# Generated by Django 4.2.20 on 2025-05-19 03:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('build_app', '0019_restore_fields'),
    ]

    operations = [
        migrations.AlterField(
            model_name='buildjob',
            name='current_step',
            field=models.CharField(choices=[('CONFIG', '配置注入'), ('COCOS', 'Cocos打包'), ('ANDROID', 'Android构建'), ('COMPLETED', '完成')], default='CONFIG', help_text='构建流程中的当前步骤，用于跟踪执行进度', max_length=20, verbose_name='当前步骤'),
        ),
        migrations.AlterField(
            model_name='buildjob',
            name='debug_info',
            field=models.TextField(blank=True, help_text='用于存储详细的调试信息，便于排查问题', null=True, verbose_name='调试信息'),
        ),
    ]
