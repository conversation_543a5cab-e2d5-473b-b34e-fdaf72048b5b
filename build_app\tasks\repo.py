"""
仓库同步模块，负责 Git 仓库的克隆与更新。
"""
import logging
from pathlib import Path
from urllib.parse import urlparse, urlunparse

from celery import shared_task
from django.conf import settings

from ..models import Project
from .base import get_system_config, run_command

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=1)
def sync_project_repo(self, project_id):
    """克隆或更新项目的 Git 仓库（包括子模块）并更新状态。
    
    该任务会根据项目是否已存在本地仓库，执行克隆或更新操作。
    整个过程中会更新项目同步状态，记录详细日志，并妥善处理各种异常情况。
    
    Args:
        project_id: 项目 ID
        
    Returns:
        执行结果消息
    """
    project = None  # 初始化项目为 None
    try:
        project = Project.objects.get(pk=project_id)
        logger.info(f"开始同步项目: {project.name} (ID: {project_id})")

        # 更新状态为 SYNCING
        project.sync_status = Project.SyncStatus.SYNCING
        project.sync_message = "正在准备同步..."
        project.save(update_fields=['sync_status', 'sync_message'])

        # --- 获取配置 ---
        projects_base_dir = get_system_config('projects_base_dir', 'projects')
        gitee_token = get_system_config('gitee_access_token', None)

        # --- 确定本地路径 ---
        if project.local_path:
            local_path = Path(project.local_path)
        else:
            base_path = Path(settings.BASE_DIR) / projects_base_dir
            sanitized_name = "".join(c if c.isalnum() or c in ('-', '_') else '_' for c in project.name)
            local_path = base_path / sanitized_name

        local_path.mkdir(parents=True, exist_ok=True)
        local_path_str = str(local_path)

        # --- 构建仓库 URL（如适用，添加 token） ---
        repo_url = project.repository_url
        parsed_url = urlparse(repo_url)
        if 'gitee.com' in parsed_url.netloc and gitee_token and gitee_token != 'your_gitee_access_token':
            netloc_with_token = f"{gitee_token}@{parsed_url.netloc}"
            repo_url = urlunparse(parsed_url._replace(netloc=netloc_with_token))
            logger.info("使用 Gitee token 进行认证。")
        elif 'gitee.com' in parsed_url.netloc:
            logger.warning("检测到 Gitee URL 但未找到有效 token。假设为公开仓库或使用 SSH 密钥认证。")

        # --- 执行 Git 命令 ---
        git_dir = local_path / '.git'
        branch = project.main_branch
        final_message = ""  # 存储最终成功/错误消息

        if git_dir.exists() and git_dir.is_dir():
            # 更新现有仓库
            return _update_existing_repo(project, local_path_str, branch)
        else:
            # 克隆新仓库
            return _clone_new_repo(project, local_path_str, repo_url, branch)

    except Project.DoesNotExist:
        logger.error(f"未找到 ID 为 {project_id} 的项目。")
        # 没有项目对象可更新状态
        return f"未找到 ID 为 {project_id} 的项目。"  # Celery 任务结果

    except Exception as e:
        error_message = f"同步项目 {project_id} 时发生意外错误: {e}"
        logger.exception(error_message)  # 记录完整堆栈跟踪
        if project:  # 检查是否已获取项目对象
            project.sync_status = Project.SyncStatus.FAILED
            project.sync_message = error_message
            project.save(update_fields=['sync_status', 'sync_message'])
        # 如果是潜在的暂时性错误，则重试任务（可选）
        try:
            self.retry(exc=e, countdown=60)
        except self.MaxRetriesExceededError:
            logger.error(f"项目 {project_id} 的最大重试次数已用尽。放弃。")
            return f"重试多次后失败: {e}"  # 重试后的最终任务结果
        return f"发生意外错误，正在重试: {e}"  # 重试前的任务结果


def _update_existing_repo(project, local_path_str, branch):
    """更新已存在的仓库。
    
    Args:
        project: 项目对象
        local_path_str: 本地路径字符串
        branch: 分支名
        
    Returns:
        执行结果消息
    """
    logger.info(f"仓库已存在于 {local_path_str}。正在更新...")
    project.sync_message = "正在更新现有仓库..."
    project.save(update_fields=['sync_message'])

    # 1. 获取更改
    success, msg, return_code = run_command(['git', 'fetch', 'origin'], cwd=local_path_str)
    if not success:
        final_message = f"错误：获取远程更改失败。\n{msg}"
        project.sync_status = Project.SyncStatus.FAILED
        project.sync_message = final_message
        project.save(update_fields=['sync_status', 'sync_message'])
        return final_message  # 停止执行

    # 2. 检出目标分支
    success, msg, return_code = run_command(['git', 'checkout', branch], cwd=local_path_str)
    # 忽略 'Already on' 或 'Switched to branch' 信息消息，但对真正的错误失败
    if not success and 'Already on' not in msg and 'Switched to branch' not in msg:
        final_message = f"错误：切换到分支 '{branch}' 失败。\n{msg}"
        project.sync_status = Project.SyncStatus.FAILED
        project.sync_message = final_message
        project.save(update_fields=['sync_status', 'sync_message'])
        return final_message  # 停止执行

    # 3. 拉取分支的更改
    success, msg, return_code = run_command(['git', 'pull', 'origin', branch], cwd=local_path_str)
    if not success:
        final_message = f"错误：拉取分支 '{branch}' 失败。\n{msg}"
        project.sync_status = Project.SyncStatus.FAILED
        project.sync_message = final_message
        project.save(update_fields=['sync_status', 'sync_message'])
        return final_message  # 停止执行

    # 4. 更新子模块
    logger.info("正在更新子模块...")
    project.sync_message = "正在更新子模块..."
    project.save(update_fields=['sync_message'])
    success, msg, return_code = run_command(['git', 'submodule', 'update', '--init', '--recursive'], cwd=local_path_str)
    if not success:
        final_message = f"错误：更新子模块失败。\n{msg}"
        project.sync_status = Project.SyncStatus.FAILED
        project.sync_message = final_message
        project.save(update_fields=['sync_status', 'sync_message'])
        return final_message  # 停止执行

    final_message = f"项目 '{project.name}' 代码更新成功。"
    project.sync_status = Project.SyncStatus.SUCCESS
    project.sync_message = final_message
    project.save(update_fields=['sync_status', 'sync_message'])
    logger.info(final_message)
    return final_message


def _clone_new_repo(project, local_path_str, repo_url, branch):
    """克隆新仓库。
    
    Args:
        project: 项目对象
        local_path_str: 本地路径字符串
        repo_url: 仓库 URL
        branch: 分支名
        
    Returns:
        执行结果消息
    """
    logger.info(f"仓库不存在于 {local_path_str}。正在克隆...")
    project.sync_message = "正在克隆新仓库..."
    project.save(update_fields=['sync_message'])

    clone_command = [
        'git', 'clone',
        '--branch', branch,
        '--recurse-submodules',  # 递归克隆子模块
        repo_url,
        local_path_str
    ]
    success, msg, return_code = run_command(clone_command)
    if not success:
        final_message = f"错误：克隆仓库失败。\n{msg}"
        project.sync_status = Project.SyncStatus.FAILED
        project.sync_message = final_message
        project.save(update_fields=['sync_status', 'sync_message'])
        return final_message  # 停止执行

    final_message = f"项目 '{project.name}' 代码克隆成功。"
    project.sync_status = Project.SyncStatus.SUCCESS
    project.sync_message = final_message
    project.save(update_fields=['sync_status', 'sync_message'])
    logger.info(final_message)
    return final_message
