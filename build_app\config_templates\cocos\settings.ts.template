// Cocos Creator buildConfig.ts template
// Placeholders like {{ app_name }} will be replaced during build.

export default {
    appName: "{{ app_name }}", // 应用名称
    baseUrl: "{{ base_url }}", // 基础 URL
    adSelection: "{{ cocos_ad_platform }}", // 广告平台
    loginState: "{{ cocos_login_type }}", // 登录类型
    isAk: {{ is_ak }},
    Debug_log: {{ debug_log|lower }}, // 使用数据库中的调试模式设置
    invite_code: "{{ invite_code }}", // 代理码
    // 其他 Cocos 相关配置...
    // 使用 extra_data 中的值: {# {{ extra_data.some_cocos_key | default_value }} #}
};
