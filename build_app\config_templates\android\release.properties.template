# Android release properties template
# Placeholders like {{ application_id }} will be replaced during build.

# Application ID
applicationId={{ application_id }}
# Application Name
appName={{ app_name }}
# Keystore Information
storeFile={{ store_file_path }}
# Note: Generated password
storePassword={{ store_password }}
# Note: Derived from keystore_base_name
keyAlias={{ key_alias }}
# Note: Generated password
keyPassword={{ key_password }}

# Version Information
versionCode={{ version_code }}
versionName={{ version_name }}

# Other Android specific properties from AppConfig
onDisplayDesktop={{ android_hide_icon_str }}
ak={{ baidu_location_ak }}
adPlatform={{ adPlatform }}
icon_path={{ icon_path }}
app_id={{ weixin_app_id }}
app_secret={{ weixin_app_secret }}
