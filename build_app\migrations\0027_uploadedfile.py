# Generated by Django 4.2.20 on 2025-05-28 06:40

from django.db import migrations, models
import django.utils.timezone
import model_utils.fields


class Migration(migrations.Migration):

    dependencies = [
        ('build_app', '0026_alter_appconfig_cocos_ad_platform'),
    ]

    operations = [
        migrations.CreateModel(
            name='UploadedFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('company_name', models.CharField(help_text='文件所属公司名称', max_length=100, verbose_name='公司名称')),
                ('project_name', models.CharField(help_text='文件所属项目名称', max_length=100, verbose_name='项目名称')),
                ('config_name', models.CharField(help_text='文件所属应用配置名称', max_length=100, verbose_name='配置名称')),
                ('app_name', models.CharField(help_text='应用的显示名称', max_length=100, verbose_name='应用显示名称')),
                ('file_name', models.CharField(help_text='上传的文件名', max_length=255, verbose_name='文件名')),
                ('file_size', models.IntegerField(default=0, help_text='上传文件的大小(字节)', verbose_name='文件大小(字节)')),
                ('download_link', models.URLField(help_text='文件的下载链接', max_length=255, verbose_name='下载链接')),
                ('remote_path', models.CharField(help_text='文件在远程服务器上的路径', max_length=255, verbose_name='远程路径')),
                ('remote_server', models.CharField(help_text='上传到的远程服务器地址', max_length=255, verbose_name='远程服务器')),
            ],
            options={
                'verbose_name': '上传记录',
                'verbose_name_plural': '上传记录',
                'ordering': ['-created'],
            },
        ),
    ]
