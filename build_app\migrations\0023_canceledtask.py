# Generated by Django 4.2.20 on 2025-05-20 07:47

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('build_app', '0022_fix_duplicate_column'),
    ]

    operations = [
        migrations.CreateModel(
            name='CanceledTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.CharField(max_length=100, unique=True, verbose_name='任务ID')),
                ('canceled_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='取消时间')),
            ],
            options={
                'verbose_name': '已取消任务',
                'verbose_name_plural': '已取消任务',
            },
        ),
    ]
