# Generated by Django 4.2.20 on 2025-05-16 06:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('build_app', '0015_remove_appconfig_android_use_csj_sdk'),
    ]

    operations = [
        migrations.CreateModel(
            name='FileManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': '文件管理',
                'verbose_name_plural': '文件管理',
                'managed': False,
            },
        ),
        migrations.AddField(
            model_name='buildjob',
            name='current_step',
            field=models.CharField(choices=[('CONFIG', '配置注入'), ('COCOS', 'Cocos打包'), ('ANDROID', 'Android构建'), ('COMPLETED', '完成')], default='CONFIG', help_text='构建流程中的当前步骤，用于跟踪执行进度', max_length=20, verbose_name='当前步骤'),
        ),
        migrations.AddField(
            model_name='buildjob',
            name='debug_info',
            field=models.TextField(blank=True, help_text='用于存储详细的调试信息，便于排查问题', null=True, verbose_name='调试信息'),
        ),
        migrations.AlterField(
            model_name='appconfig',
            name='android_hide_icon',
            field=models.BooleanField(default=False, verbose_name='是否隐藏桌面图标 (Android)'),
        ),
    ]
