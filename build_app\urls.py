from django.urls import path
from . import views

app_name = 'build_app'

urlpatterns = [
    # 构建触发页面
    path('trigger/', views.trigger_build_view, name='trigger_build'), 
    
    # 构建日志查看页面（移除了列表页面，只保留详情页）
    path('logs/<int:build_job_id>/', views.build_log_view, name='build_log_detail'),
    
    # AJAX URLs
    path('ajax/get_projects/', views.ajax_get_projects, name='ajax_get_projects'),
    path('ajax/get_configs/', views.ajax_get_configs, name='ajax_get_configs'),
    path('ajax/build_log/<int:build_job_id>/', views.ajax_get_build_log, name='ajax_build_log'),
]
