# d:\sanlian\AIProject\DjangoAutoBuild\build_app\utils.py

import os
import typing
from pathlib import Path
from django.template import Context, Template
from django.template.loader import get_template, TemplateDoesNotExist
from django.conf import settings # Potentially needed for base paths later

# --- Type Hinting Imports --- (Only used by type checkers)
if typing.TYPE_CHECKING:
    from .models import BuildJob, Project, AppConfig
# --------------------------

def generate_cocos_config(app_config, build_job=None):
    """
    生成Cocos项目的配置文件内容（buildConfig.ts）
    
    Args:
        app_config: 应用配置对象
        build_job: 可选的构建任务对象，用于记录日志
        
    Returns:
        配置文件内容字符串
    """
    try:
        # 处理 base_url，确保它以 https:// 开头并以 / 结尾
        base_url = app_config.base_url.strip() if app_config.base_url else ''

        # 如果 URL 以 http:// 开头，替换为 https://
        if base_url and base_url.startswith('http://'):
            base_url = 'https://' + base_url[7:]
        
        # 如果 URL 不以 http:// 或 https:// 开头，添加 https://
        if base_url and not (base_url.startswith('http://') or base_url.startswith('https://')):
            base_url = 'https://' + base_url
        
        # 确保 URL 以 / 结尾
        if base_url and not base_url.endswith('/'):
            base_url += '/'
        
        # 计算版本名
        version_code = app_config.version_code_base
        major = version_code // 100
        minor = (version_code % 100) // 10
        patch = version_code % 10
        version_name = f"{major}.{minor}.{patch}"
        
        # 准备模板上下文
        context_data = {
            # 基本应用信息
            'app_name': app_config.app_name,
            'application_id': app_config.application_id,
            'version_name': version_name,
            'version_code': version_code,
            
            # API配置
            'base_url': base_url,
            'baidu_location_ak': app_config.baidu_location_ak or '',
            
            # 登录和广告配置
            'cocos_login_type': app_config.cocos_login_type or 'none',
            'cocos_ad_platform': app_config.cocos_ad_platform or 'android',
            
            # 新增字段
            'debug_log': app_config.debug_log,
            'invite_code': app_config.invite_code or '',
            'adPlatform': app_config.cocos_ad_platform or 'android',
            
            # 布尔值配置
            'is_ak': str(app_config.is_ak).lower(),
            'android_hide_icon': str(app_config.android_hide_icon).lower(),
        }
        
        # 添加extra_data中的值（如果有）
        if hasattr(app_config, 'extra_data') and app_config.extra_data:
            context_data.update(app_config.extra_data)
        
        # 使用模板系统渲染配置文件
        try:
            from django.template import Context, Template
            from django.template.loader import get_template, TemplateDoesNotExist
            
            template = get_template('cocos/settings.ts.template')
            config_content = template.render(context_data)
            
            if build_job:
                build_job.log += f"已使用模板生成Cocos配置内容\n"
                
            return config_content
        except Exception as e:
            logger.error(f"模板渲染失败: {e}")
            raise
            
    except Exception as e:
        if build_job:
            build_job.log += f"生成Cocos配置失败: {e}\n"
        logger.error(f"Failed to generate Cocos config: {e}")
        raise

def generate_android_properties(app_config, build_job=None):
    """
    生成Android项目的配置文件内容（release.properties）
    
    Args:
        app_config: 应用配置对象
        build_job: 可选的构建任务对象，用于记录日志
        
    Returns:
        配置文件内容字符串
    """
    try:
        # 处理 base_url，确保它以 https:// 开头并以 / 结尾
        base_url = app_config.base_url.strip() if app_config.base_url else ''

        if base_url and base_url.startswith('http://'):
            base_url = 'https://' + base_url[7:]
        
        if base_url and not (base_url.startswith('http://') or base_url.startswith('https://')):
            base_url = 'https://' + base_url
        
        if base_url and not base_url.endswith('/'):
            base_url += '/'
        
        # 计算版本名
        version_code = app_config.version_code_base
        major = version_code // 100
        minor = (version_code % 100) // 10
        patch = version_code % 10
        version_name = f"{major}.{minor}.{patch}"
        
        # 准备上下文数据
        context_data = {
            # 基本应用信息
            'app_name': app_config.app_name,
            'application_id': app_config.application_id,
            'version_name': version_name,
            'version_code': str(version_code),
            
            # API配置
            'base_url': base_url,
            'baidu_location_ak': app_config.baidu_location_ak or '',  # 添加百度定位AK
            
            # 微信相关配置
            'weixin_app_id': app_config.weixin_app_id or '',  # 微信应用ID
            'weixin_app_secret': app_config.weixin_app_secret or '',  # 微信应用密钥
            
            # 新增字段
            'debug_log': app_config.debug_log,
            'invite_code': app_config.invite_code or '',
            'adPlatform': app_config.cocos_ad_platform or 'android',
            
            # 布尔值配置
            'android_hide_icon_str': str(app_config.android_hide_icon).lower(),
        }
        
        # 添加keystore相关信息
        import os
        from django.conf import settings
        
        # 设置默认值
        context_data['store_file_path'] = ''
        context_data['key_alias'] = ''
        context_data['store_password'] = ''
        context_data['key_password'] = ''
        
        if app_config.keystore_base_name:
            keystore_name = app_config.keystore_base_name
            password = f"{keystore_name}168"
            
            # 密钥库文件路径优先级判断
            store_file_path = ''
            
            # 1. 首先检查旧路径
            old_jks_path = f"D:/Cocos/CocosForAndroid/AndroidAppJKSs/{keystore_name}.jks"
            if os.path.exists(old_jks_path):
                store_file_path = old_jks_path
                if build_job:
                    build_job.log += f"使用旧路径密钥库文件: {old_jks_path}\n"
            
            # 2. 然后检查是否有上传的密钥库文件
            elif app_config.keystore_file and app_config.keystore_file.name:
                store_file_path = os.path.join(settings.MEDIA_ROOT, app_config.keystore_file.name).replace('\\', '/')
                if build_job:
                    build_job.log += f"使用上传的密钥库文件: {app_config.keystore_file.name}\n"
            
            # 3. 最后使用生成的路径
            else:
                jks_file_absolute_path = app_config.app_media_base_dir / f"{keystore_name}.jks"
                store_file_path = str(jks_file_absolute_path).replace('\\', '/')
                if build_job:
                    build_job.log += f"使用生成的密钥库路径: {store_file_path}\n"
            
            context_data['store_file_path'] = store_file_path
            context_data['key_alias'] = keystore_name
            context_data['store_password'] = password
            context_data['key_password'] = password
            
            # 如果有必要，根据情况创建密钥库文件
            if not os.path.exists(store_file_path) and app_config.keystore_base_name and not app_config.keystore_file:
                if build_job:
                    build_job.log += f"密钥库文件不存在，将自动生成: {store_file_path}\n"
                # 这里可以添加生成密钥库的逻辑 (已在其他地方实现)
        else:
            context_data['store_file_path'] = ''
            context_data['key_alias'] = ''
            context_data['store_password'] = ''
            context_data['key_password'] = ''
        
        # 添加图标路径
        res_dir_name = f"res-{app_config._safe_app_name_for_path}"
        icon_path = str(app_config.app_media_base_dir / res_dir_name).replace('\\', '/')
        context_data['icon_path'] = icon_path
        
        # 添加extra_data中的值（如果有）
        if hasattr(app_config, 'extra_data') and app_config.extra_data:
            context_data.update(app_config.extra_data)
            
        # 使用模板系统渲染配置文件
        try:
            from django.template import Context, Template
            from django.template.loader import get_template, TemplateDoesNotExist
            
            template = get_template('android/release.properties.template')
            config_content = template.render(context_data)
            
            if build_job:
                build_job.log += f"已使用模板生成Android配置内容\n"
                
            return config_content
        except Exception as e:
            logger.error(f"模板渲染失败: {e}")
            raise
            
    except Exception as e:
        if build_job:
            build_job.log += f"生成Android配置失败: {e}\n"
        logger.error(f"Failed to generate Android properties: {e}")
        raise

def generate_config_files(build_job: 'BuildJob', project_code_path: str):
    """
    Generates configuration files based on templates and BuildJob data.

    Args:
        build_job: The BuildJob instance containing project and app_config info.
        project_code_path: The absolute path to the checked-out project code directory.
    """
    # Import models locally
    from .models import BuildJob, Project, AppConfig

    project = build_job.project
    app_config = build_job.app_config

    if not project or not app_config:
        raise ValueError("BuildJob is missing Project or AppConfig association.")

    if not os.path.isdir(project_code_path):
        raise FileNotFoundError(f"Project code path does not exist or is not a directory: {project_code_path}")

    # --- Generate files based on project type ---
    try:
        if project.project_type == 'COCOS':
            # 1. Generate Cocos settings file (buildConfig.ts)
            cocos_config = generate_cocos_config(app_config, build_job)
            cocos_target_abs_path = os.path.join(project_code_path, 'assets', 'buildConfig.ts')
            
            # Ensure target directory exists
            os.makedirs(os.path.dirname(cocos_target_abs_path), exist_ok=True)
            
            with open(cocos_target_abs_path, 'w', encoding='utf-8') as f:
                f.write(cocos_config)
            print(f"Successfully generated Cocos config file: {cocos_target_abs_path}")

            # 2. Generate Android properties file (release.properties) in project root
            android_config = generate_android_properties(app_config, build_job)
            android_props_target_abs_path = os.path.join(project_code_path, 'release.properties')
            
            with open(android_props_target_abs_path, 'w', encoding='utf-8') as f:
                f.write(android_config)
            print(f"Successfully generated Android properties file: {android_props_target_abs_path}")

        elif project.project_type == 'ANDROID':
            # Generate only Android properties file for pure Android projects
            android_config = generate_android_properties(app_config, build_job)
            android_props_target_abs_path = os.path.join(project_code_path, 'release.properties')
            
            with open(android_props_target_abs_path, 'w', encoding='utf-8') as f:
                f.write(android_config)
            print(f"Successfully generated Android properties file: {android_props_target_abs_path}")

        else:
            print(f"Skipping config file generation for project type: {project.project_type}")

    except Exception as e:
        # Catch exceptions from rendering or file writing
        error_msg = f"ERROR: Failed during config file generation process for Job ID {build_job.id}: {e}"
        print(error_msg)
        build_job.log += error_msg + "\n"
        # Re-raise to be caught by the calling view
        raise

import subprocess
import logging
from pathlib import Path

# Ensure logger is configured, or at least get a logger instance
logger = logging.getLogger(__name__)

def generate_jks_keystore(
    alias: str,
    store_password: str,
    key_password: str,
    dname: str,
    output_jks_path: Path,
    validity_days: int = 3650,
    key_algorithm: str = "RSA",
    key_size: int = 2048,
    store_type: str = "JKS"
) -> bool:
    """
    Generates a JKS keystore using the keytool command-line utility.

    Args:
        alias (str): The alias for the key entry in the keystore.
        store_password (str): The password for accessing the keystore.
        key_password (str): The password for protecting the key entry.
        dname (str): The distinguished name string for the certificate
            (e.g., "CN=MyCompany, OU=Dev, O=MyOrg, L=GZ, ST=GZ, C=ZH").
        output_jks_path (Path): The full pathlib.Path object 세율്the JKS file will be saved.
                                The parent directories will be created if they don't exist.
        validity_days (int): Validity period of the certificate in days. Default is 3650 (10 years).
        key_algorithm (str): Key algorithm to use (e.g., "RSA", "DSA"). Default is "RSA".
        key_size (int): Size of the key. Default is 2048.
        store_type (str): Type of the keystore (e.g., "JKS", "PKCS12"). Default is "JKS".

    Returns:
        bool: True if the JKS keystore was generated successfully, False otherwise.
    """
    try:
        output_jks_path.parent.mkdir(parents=True, exist_ok=True)

        if output_jks_path.exists():
            try:
                output_jks_path.unlink()
                logger.info(f"Removed existing keystore at {output_jks_path} for overwrite.")
            except OSError as e:
                logger.error(f"Error removing existing keystore {output_jks_path}: {e}")
                return False

        keytool_cmd = [
            "keytool",
            "-genkeypair",
            "-alias", alias,
            "-keyalg", key_algorithm,
            "-keysize", str(key_size),
            "-keystore", str(output_jks_path),
            "-storepass", store_password,
            "-keypass", key_password,
            "-dname", dname,
            "-validity", str(validity_days),
            "-storetype", store_type,
        ]

        logger.info(f"Attempting to generate JKS keystore: {' '.join(keytool_cmd)}")
        
        process = subprocess.run(keytool_cmd, capture_output=True, text=True, check=False)

        if process.returncode == 0:
            logger.info(f"Successfully generated JKS keystore at: {output_jks_path}")
            return True
        else:
            logger.error(f"Failed to generate JKS keystore. keytool exited with code {process.returncode}.")
            logger.error(f"keytool stdout: {process.stdout.strip()}")
            logger.error(f"keytool stderr: {process.stderr.strip()}")
            return False

    except FileNotFoundError:
        logger.error(
            "The 'keytool' command was not found. "
            "Please ensure Java JDK is installed and 'keytool' is in the system PATH."
        )
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred while generating JKS keystore: {e}")
        return False
