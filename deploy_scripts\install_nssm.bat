@echo off
chcp 65001 > nul
REM ======================================================
REM NSSM (Non-Sucking Service Manager)安装脚本
REM 此脚本用于下载NSSM并安装DjangoAutoBuild服务
REM ======================================================

echo ===== NSSM安装和服务配置脚本 =====
echo.

set NSSM_VERSION=2.24
set NSSM_URL=https://nssm.cc/release/nssm-%NSSM_VERSION%.zip
set DOWNLOAD_DIR=%~dp0downloads
set NSSM_ZIP=%DOWNLOAD_DIR%\nssm-%NSSM_VERSION%.zip
set NSSM_DIR=%~dp0..\nssm
set PROJECT_DIR=%~dp0..

REM 创建下载目录
if not exist %DOWNLOAD_DIR% mkdir %DOWNLOAD_DIR%

REM 检查是否已下载
if not exist %NSSM_ZIP% (
    echo 正在下载NSSM %NSSM_VERSION%...
    powershell -Command "& {Invoke-WebRequest -Uri '%NSSM_URL%' -OutFile '%NSSM_ZIP%'}"
    if %ERRORLEVEL% NEQ 0 (
        echo [错误] 下载NSSM失败。请手动下载:
        echo %NSSM_URL%
        echo 并将文件保存到: %NSSM_ZIP%
        exit /b 1
    )
    echo [√] NSSM下载完成
) else (
    echo [√] NSSM安装包已存在: %NSSM_ZIP%
)

REM 解压NSSM
if not exist %NSSM_DIR% (
    echo 正在解压NSSM...
    mkdir %NSSM_DIR%
    powershell -Command "& {Expand-Archive -Path '%NSSM_ZIP%' -DestinationPath '%NSSM_DIR%' -Force}"
    if %ERRORLEVEL% NEQ 0 (
        echo [错误] 解压NSSM失败。请手动解压:
        echo %NSSM_ZIP%
        echo 到目录: %NSSM_DIR%
        exit /b 1
    )
    echo [√] NSSM解压完成
) else (
    echo [√] NSSM目录已存在: %NSSM_DIR%
)

REM 确定NSSM可执行文件路径
set NSSM_EXE=
if exist "%NSSM_DIR%\nssm-%NSSM_VERSION%\win64\nssm.exe" (
    set NSSM_EXE=%NSSM_DIR%\nssm-%NSSM_VERSION%\win64\nssm.exe
) else if exist "%NSSM_DIR%\nssm-%NSSM_VERSION%\win32\nssm.exe" (
    set NSSM_EXE=%NSSM_DIR%\nssm-%NSSM_VERSION%\win32\nssm.exe
) else (
    echo [错误] 未找到nssm.exe。请检查解压是否正确。
    exit /b 1
)

echo [√] 找到NSSM可执行文件: %NSSM_EXE%
echo.

REM 检查服务启动脚本是否存在
if not exist "%PROJECT_DIR%\run_server.bat" (
    echo [错误] 未找到Web服务启动脚本: %PROJECT_DIR%\run_server.bat
    echo 请先运行install_windows.bat创建启动脚本。
    exit /b 1
)

if not exist "%PROJECT_DIR%\run_celery.bat" (
    echo [错误] 未找到Celery Worker启动脚本: %PROJECT_DIR%\run_celery.bat
    echo 请先运行install_windows.bat创建启动脚本。
    exit /b 1
)

echo ===== 安装DjangoAutoBuild服务 =====
echo.

REM 检查服务是否已存在
%NSSM_EXE% status DjangoAutoBuild_Web >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [!] DjangoAutoBuild_Web服务已存在。
    set /p REINSTALL_WEB="是否重新安装? (y/n): "
    if /i "%REINSTALL_WEB%"=="y" (
        echo 正在移除现有服务...
        %NSSM_EXE% remove DjangoAutoBuild_Web confirm
    ) else (
        echo 跳过Web服务安装。
        goto :CELERY_SERVICE
    )
)

echo 正在安装DjangoAutoBuild_Web服务...
%NSSM_EXE% install DjangoAutoBuild_Web "%PROJECT_DIR%\run_server.bat"
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 安装Web服务失败。
    exit /b 1
)

REM 配置Web服务
%NSSM_EXE% set DjangoAutoBuild_Web DisplayName "DjangoAutoBuild Web服务"
%NSSM_EXE% set DjangoAutoBuild_Web Description "DjangoAutoBuild项目的Web服务器"
%NSSM_EXE% set DjangoAutoBuild_Web AppDirectory "%PROJECT_DIR%"
%NSSM_EXE% set DjangoAutoBuild_Web AppStdout "%PROJECT_DIR%\logs\web_stdout.log"
%NSSM_EXE% set DjangoAutoBuild_Web AppStderr "%PROJECT_DIR%\logs\web_stderr.log"
%NSSM_EXE% set DjangoAutoBuild_Web AppRotateFiles 1
%NSSM_EXE% set DjangoAutoBuild_Web AppRotateOnline 1
%NSSM_EXE% set DjangoAutoBuild_Web AppRotateSeconds 86400
%NSSM_EXE% set DjangoAutoBuild_Web Start SERVICE_AUTO_START

echo [√] DjangoAutoBuild_Web服务安装完成

:CELERY_SERVICE
REM 检查Celery服务是否已存在
%NSSM_EXE% status DjangoAutoBuild_Celery >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [!] DjangoAutoBuild_Celery服务已存在。
    set /p REINSTALL_CELERY="是否重新安装? (y/n): "
    if /i "%REINSTALL_CELERY%"=="y" (
        echo 正在移除现有服务...
        %NSSM_EXE% remove DjangoAutoBuild_Celery confirm
    ) else (
        echo 跳过Celery服务安装。
        goto :SERVICE_START
    )
)

echo 正在安装DjangoAutoBuild_Celery服务...
%NSSM_EXE% install DjangoAutoBuild_Celery "%PROJECT_DIR%\run_celery.bat"
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 安装Celery服务失败。
    exit /b 1
)

REM 配置Celery服务
%NSSM_EXE% set DjangoAutoBuild_Celery DisplayName "DjangoAutoBuild Celery服务"
%NSSM_EXE% set DjangoAutoBuild_Celery Description "DjangoAutoBuild项目的Celery Worker服务"
%NSSM_EXE% set DjangoAutoBuild_Celery AppDirectory "%PROJECT_DIR%"
%NSSM_EXE% set DjangoAutoBuild_Celery AppStdout "%PROJECT_DIR%\logs\celery_stdout.log"
%NSSM_EXE% set DjangoAutoBuild_Celery AppStderr "%PROJECT_DIR%\logs\celery_stderr.log"
%NSSM_EXE% set DjangoAutoBuild_Celery AppRotateFiles 1
%NSSM_EXE% set DjangoAutoBuild_Celery AppRotateOnline 1
%NSSM_EXE% set DjangoAutoBuild_Celery AppRotateSeconds 86400
%NSSM_EXE% set DjangoAutoBuild_Celery Start SERVICE_AUTO_START

echo [√] DjangoAutoBuild_Celery服务安装完成

:SERVICE_START
echo.
echo ===== 启动服务 =====

REM 创建日志目录
if not exist "%PROJECT_DIR%\logs" mkdir "%PROJECT_DIR%\logs"

REM 询问是否启动服务
set /p START_SERVICES="是否立即启动服务? (y/n): "
if /i "%START_SERVICES%"=="y" (
    echo 正在启动DjangoAutoBuild_Web服务...
    %NSSM_EXE% start DjangoAutoBuild_Web
    if %ERRORLEVEL% NEQ 0 (
        echo [警告] 启动Web服务失败。请检查日志文件。
    ) else (
        echo [√] DjangoAutoBuild_Web服务已启动
    )
    
    echo 正在启动DjangoAutoBuild_Celery服务...
    %NSSM_EXE% start DjangoAutoBuild_Celery
    if %ERRORLEVEL% NEQ 0 (
        echo [警告] 启动Celery服务失败。请检查日志文件。
    ) else (
        echo [√] DjangoAutoBuild_Celery服务已启动
    )
) else (
    echo 服务安装完成，但未启动。
)

echo.
echo ===== 服务安装完成 =====
echo.
echo 服务管理命令:
echo - 启动服务: %NSSM_EXE% start DjangoAutoBuild_Web
echo - 停止服务: %NSSM_EXE% stop DjangoAutoBuild_Web
echo - 重启服务: %NSSM_EXE% restart DjangoAutoBuild_Web
echo - 查看状态: %NSSM_EXE% status DjangoAutoBuild_Web
echo - 编辑配置: %NSSM_EXE% edit DjangoAutoBuild_Web
echo.
echo 同样的命令也适用于DjangoAutoBuild_Celery服务。
echo.
echo 日志文件位置: %PROJECT_DIR%\logs\
echo.
echo 感谢使用!
