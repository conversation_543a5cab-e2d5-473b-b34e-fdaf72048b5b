"""
管理日志类
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _


class LogEntryAdmin(admin.ModelAdmin):
    """管理日志查看类 - 只读模式"""
    list_display = [
        'action_time',
        'user',
        'content_type',
        'object_repr',
        'action_flag_display',
        'change_message',
    ]
    list_filter = ['action_time', 'user', 'content_type']
    search_fields = ['object_repr', 'change_message']
    date_hierarchy = 'action_time'
    readonly_fields = [
        'action_time', 'user', 'content_type', 'object_id',
        'object_repr', 'action_flag', 'change_message'
    ]
    
    def action_flag_display(self, obj):
        """将操作类型代码转换为可读文本"""
        flags = {
            1: '添加',
            2: '修改',
            3: '删除',
        }
        return flags.get(obj.action_flag, obj.action_flag)
    action_flag_display.short_description = '操作类型'
    
    def has_add_permission(self, request):
        """禁止添加日志记录"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁止修改日志记录"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """禁止删除日志记录"""
        return False
